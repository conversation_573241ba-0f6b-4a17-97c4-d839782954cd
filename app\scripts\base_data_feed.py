import pandas as pd
from abc import ABC, abstractmethod
from typing import Optional


class DataFeed(ABC):
    """Abstract class for providing candle data (historical or real-time)."""
    @abstractmethod
    async def connect(self):
        """Establish connection to the data source."""
        pass

    @abstractmethod
    async def get_next_candle(self) -> Optional[pd.Series]:
        """Fetch the next candle for processing."""
        pass
