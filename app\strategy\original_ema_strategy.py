import logging
from app.strategy.base_strategy import BaseStrategy
# from app.trade_manager.trade_manager import TradeManager - unused import
# from app.trade_manager.portfolio_manager import PortfolioManager - unused import
import pandas as pd
from app.utils.indicator_calculator import IndicatorCalculator

logger = logging.getLogger(__name__)


class OriginalEMAStrategy(BaseStrategy):
    """EMA-based strategy using EMA bounce, breakout, and volume exits with Random Forest uptrend prediction."""

    def __init__(self, pair: str, short_window: int = 5, long_window: int = 13,
                 take_profit_levels: list = [0.005, 0.01, 0.015, 0.02, 0.025],
                 stop_loss: float = -0.005,
                 interval: str = '1h',
                 investment_percentage: float = 0.8,
                 max_investment: float = 7500,
                 taker_maker_fee: float = 0.0025,
                 min_investment: float = 200,
                 min_body_ratio=0.6,
                 max_upper_wick_ratio=0.2,
                 min_volume_multiplier=1.2,
                 pullback_threshold=0.985  # 0.985 1.5% pullback
                 ):
        super().__init__(pair, take_profit_levels=take_profit_levels, stop_loss=stop_loss, interval=interval,
                         max_investment=max_investment, taker_maker_fee=taker_maker_fee, min_investment=min_investment)
        # Store investment_percentage as an instance variable
        self.investment_percentage = investment_percentage

        self.short_window = short_window
        self.long_window = long_window
        self.candle_counter = 0
        self.cooldown_candles = 0
        self.min_body_ratio = min_body_ratio
        self.max_upper_wick_ratio = max_upper_wick_ratio
        self.min_volume_multiplier = min_volume_multiplier
        self.pullback_threshold = pullback_threshold
        self.df = pd.DataFrame()

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        # df = self.resample_data(df)
        df["EMA_short"] = IndicatorCalculator.calculate_ema(
            df, self.short_window)  # EMA5
        df["EMA_long"] = IndicatorCalculator.calculate_ema(
            df, self.long_window)    # EMA13
        df["ema50"] = IndicatorCalculator.calculate_ema(df, 50)
        df["ema200"] = IndicatorCalculator.calculate_ema(df, 200)
        df["ema800"] = IndicatorCalculator.calculate_ema(df, 800)
        df["rsi"] = IndicatorCalculator.calculate_rsi(df)
        df["atr"] = IndicatorCalculator.calculate_atr(df)
        df["atr_50"] = IndicatorCalculator.calculate_atr(df, 50)
        df["vwap"] = IndicatorCalculator.calculate_vwap(df)
        df["avg_volume_20"] = df["volume"].rolling(window=20).mean()
        df["avg_atr_20"] = df["atr"].rolling(window=20).mean()
        df["body_size"] = abs(df["close_price"] - df["open_price"])
        df["total_size"] = df["high_price"] - df["low_price"]
        df["upper_wick"] = df["high_price"] - \
            df[["open_price", "close_price"]].max(axis=1)
        df["is_green"] = df["close_price"] > df["open_price"]

        # Safe division to prevent DivisionUndefined errors
        df["body_ratio"] = df.apply(
            lambda x: 0 if x["total_size"] == 0 else x["body_size"] /
            x["total_size"],
            axis=1
        )

        df["upper_wick_ratio"] = df.apply(
            lambda x: 0 if x["total_size"] == 0 else x["upper_wick"] /
            x["total_size"],
            axis=1
        )

        df["market_regime"] = IndicatorCalculator.detect_market_regime(df)

        # Check if timestamp column exists
        if 'timestamp' in df.columns:
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                df['timestamp'] = pd.to_datetime(df['timestamp'])

            # Handle timezone information correctly
            if hasattr(df['timestamp'], 'dt'):  # Ensure dt accessor is available
                if df['timestamp'].dt.tz is None:
                    # If no timezone, localize to UTC
                    df['timestamp'] = df['timestamp'].dt.tz_localize('UTC')
                else:
                    # If already has timezone, convert to UTC without reassignment
                    df['timestamp'] = df['timestamp'].dt.tz_convert('UTC')

            # Drop any rows with NaN values
            self.candle_counter = len(df)
            df = df.dropna()

        for col in df.columns:
            if col != 'timestamp' and col != 'is_green':
                df.loc[:, col] = df[col].astype(float, errors='ignore')
        return df

    def should_enter_trade(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series) -> bool:
        """Determine if a trade should be entered, combining trend + pattern + volume filters."""
        # minusthree_candle parameter is required by interface but not used in this implementation
        _ = minusthree_candle  # Suppress unused parameter warning
        entry_signal = False

        if self.candle_counter < 800:
            self.candle_counter += 1
            return False
        self.candle_counter += 1

        if self.cooldown_candles > 0:
            self.cooldown_candles -= 1
            return False

        if not self.check_trend(candle):
            return False

        avg_vol = candle["avg_volume_20"]

        # --- EMA Bounce with pullback
        if (previous_candle["close_price"] > previous_candle["ema50"] and
                candle["close_price"] > candle["ema50"]):

            for ema in ["ema50", "ema200", "ema800"]:
                # Add pullback check
                if (previous_candle["low_price"] <= previous_candle[ema] * self.pullback_threshold and
                    candle["close_price"] > candle[ema] and
                        candle["body_ratio"] > self.min_body_ratio):
                    entry_signal = True
                    break

        # --- EMA Breakout with candlestick + volume confirmation
        if not entry_signal:
            if (previous_candle["close_price"] < previous_candle["ema50"] and
                candle["close_price"] > candle["ema50"] and
                candle["is_green"] and
                candle["body_ratio"] > self.min_body_ratio and
                candle["upper_wick_ratio"] < self.max_upper_wick_ratio and
                candle["volume"] > avg_vol * self.min_volume_multiplier and
                    (self.is_bullish_engulfing(candle, previous_candle) or self.is_hammer(candle))):
                entry_signal = True

        return entry_signal

    def is_bullish_engulfing(self, candle, prev_candle):
        """Identify a bullish engulfing pattern."""
        return (prev_candle["close_price"] < prev_candle["open_price"] and
                candle["close_price"] > candle["open_price"] and
                candle["open_price"] < prev_candle["close_price"] and
                candle["close_price"] > prev_candle["open_price"])

    def is_bearish_engulfing(self, candle: pd.Series, prev_candle: pd.Series) -> bool:
        return (prev_candle["close_price"] > prev_candle["open_price"] and
                candle["close_price"] < candle["open_price"] and
                candle["open_price"] > prev_candle["close_price"] and
                candle["close_price"] < prev_candle["open_price"])

    def is_hammer(self, candle):
        """Identify a hammer candlestick pattern."""
        body_size = abs(candle["close_price"] - candle["open_price"])
        total_size = candle["high_price"] - candle["low_price"]
        lower_wick = min(candle["open_price"],
                         candle["close_price"]) - candle["low_price"]
        upper_wick = candle["high_price"] - \
            max(candle["open_price"], candle["close_price"])
        return (body_size < 0.3 * total_size and lower_wick > 2 * body_size and upper_wick < 0.1 * total_size)

    def should_exit_trade(self, candle: pd.Series, previous_candle: pd.Series) -> bool:
        """Determines if an open trade should be closed."""
        # previous_candle parameter is required by interface but not used in this implementation
        # Using _ prefix would cause method signature mismatch with base class
        _ = previous_candle  # Suppress unused parameter warning
        technical_breakdown = (candle["close_price"] < candle["ema800"])

        # Volume climax (potential reversal)
        volume_climax = candle["volume"] > candle["avg_volume_20"] * \
            3 and candle["close_price"] < candle["open_price"]

        return volume_climax or technical_breakdown

    def check_trend(self, candle: pd.Series) -> bool:
        """Validate bullish trend: price above ema50, ema200, ema800 and EMAs aligned."""
        return (candle["close_price"] > candle["ema50"] and
                candle["close_price"] > candle["ema200"] and
                candle["close_price"] > candle["ema800"] and
                candle["ema50"] > candle["ema200"] and
                candle["ema200"] > candle["ema800"])

    def calculate_position_size(self, portfolio_balance: float, atr: float, atr_50: float) -> float:
        """Dynamically adjust position size based on volatility."""
        atr_ratio = (atr / atr_50) if atr_50 > 0 else 1
        investment_size = min(
            self.max_investment, self.investment_percentage * portfolio_balance * atr_ratio)
        return max(investment_size, self.min_investment)
