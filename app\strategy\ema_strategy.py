import logging
import numpy as np
from app.strategy.base_strategy import BaseStrategy
import pandas as pd
from app.utils.indicator_calculator import IndicatorCalculator

logger = logging.getLogger(__name__)


class EMAStrategy(BaseStrategy):
    """Enhanced EMA-based strategy with improved drawdown management, adaptive position sizing,
    and advanced entry/exit criteria utilizing machine learning market regime detection."""

    def __init__(self, pair: str,
                 short_window: int = 9,
                 long_window: int = 22,
                 take_profit_levels: list = [0.30, 0.37, 0.75],
                 stop_loss: float = -0.16,
                 interval: str = '1h',
                 investment_percentage: float = 0.96,
                 max_investment: float = 5100,
                 taker_maker_fee: float = 0.0025,
                 min_investment: float = 200,
                 min_body_ratio=0.64,
                 max_upper_wick_ratio=0.28,
                 min_volume_multiplier=0.89,
                 # Optimized parameter now at 1.03 (3% pullback)
                 pullback_threshold=1.03,
                 adx_threshold=25,         # Minimum ADX for strong trend
                 max_consecutive_losses=3,  # For circuit breaker
                 ):
        super().__init__(pair, take_profit_levels=take_profit_levels, stop_loss=stop_loss, interval=interval,
                         max_investment=max_investment, taker_maker_fee=taker_maker_fee, min_investment=min_investment)
        # Store investment_percentage as an instance variable
        self.investment_percentage = investment_percentage

        self.short_window = short_window
        self.long_window = long_window
        self.candle_counter = 0
        self.cooldown_candles = 0
        self.min_body_ratio = min_body_ratio
        self.max_upper_wick_ratio = max_upper_wick_ratio
        self.min_volume_multiplier = min_volume_multiplier
        self.pullback_threshold = pullback_threshold
        self.adx_threshold = adx_threshold
        self.max_consecutive_losses = max_consecutive_losses
        self.market_regime = "neutral"
        self.historical_volatility = None
        self.trade_results = []  # Track trade outcomes
        self.df = pd.DataFrame()

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        # df = self.resample_data(df)

        # Core EMAs
        df["EMA_short"] = IndicatorCalculator.calculate_ema(
            df, self.short_window)
        df["EMA_long"] = IndicatorCalculator.calculate_ema(
            df, self.long_window)
        df["ema50"] = IndicatorCalculator.calculate_ema(df, 50)
        df["ema200"] = IndicatorCalculator.calculate_ema(df, 200)
        df["ema800"] = IndicatorCalculator.calculate_ema(df, 800)

        # Additional indicators
        df["rsi"] = IndicatorCalculator.calculate_rsi(df)
        df["adx"] = IndicatorCalculator.calculate_average_directional_index(df)
        df["atr"] = IndicatorCalculator.calculate_atr(df)
        df["atr_50"] = IndicatorCalculator.calculate_atr(df, 50)
        df["vwap"] = IndicatorCalculator.calculate_vwap(df)
        df["avg_volume_20"] = df["volume"].rolling(window=20).mean()
        df["avg_atr_20"] = df["atr"].rolling(window=20).mean()

        # Volatility metrics
        df["hv_20"] = df["close_price"].pct_change().rolling(
            window=20).std() * np.sqrt(365)
        self.historical_volatility = df["hv_20"].iloc[-1] if not df["hv_20"].empty else None

        # Candlestick analysis
        df["body_size"] = abs(df["close_price"] - df["open_price"])
        df["total_size"] = df["high_price"] - df["low_price"]
        df["upper_wick"] = df["high_price"] - \
            df[["open_price", "close_price"]].max(axis=1)
        df["lower_wick"] = df[["open_price", "close_price"]].min(
            axis=1) - df["low_price"]
        df["is_green"] = df["close_price"] > df["open_price"]

        # Safe division to prevent DivisionUndefined errors
        df["body_ratio"] = df.apply(
            lambda x: 0 if x["total_size"] == 0 else x["body_size"] /
            x["total_size"],
            axis=1
        )

        df["upper_wick_ratio"] = df.apply(
            lambda x: 0 if x["total_size"] == 0 else x["upper_wick"] /
            x["total_size"],
            axis=1
        )

        # Market regime detection
        df["market_regime_code"] = IndicatorCalculator.detect_market_regime(df)

        # Optional: keep the code in the DataFrame
        regime_map = {
            -1: "bearish",
            0: "ranging",
            1: "bullish",
            2: "volatile"
        }
        df["market_regime"] = df["market_regime_code"].map(regime_map)

        # Store last string regime
        if not df.empty:
            self.market_regime = df["market_regime"].iloc[-1]

        # Support/Resistance zones - add these to IndicatorCalculator
        df = self.add_support_resistance(df)

        # Check if timestamp column exists
        if 'timestamp' in df.columns:
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                df['timestamp'] = pd.to_datetime(df['timestamp'])

            # Handle timezone information correctly
            if hasattr(df['timestamp'], 'dt'):  # Ensure dt accessor is available
                if df['timestamp'].dt.tz is None:
                    # If no timezone, localize to UTC
                    df['timestamp'] = df['timestamp'].dt.tz_localize('UTC')
                else:
                    # If already has timezone, convert to UTC without reassignment
                    df['timestamp'] = df['timestamp'].dt.tz_convert('UTC')

        for col in df.columns:
            if col != 'timestamp' and col != 'is_green':
                df.loc[:, col] = df[col].astype(float, errors='ignore')

        # Drop any rows with NaN values
        self.candle_counter = len(df)
        df = df.dropna()

        return df

    def add_support_resistance(self, df):
        """Add support and resistance levels using volume profile analysis."""
        # Create volume profile
        min_price = df['low_price'].min()
        max_price = df['high_price'].max()
        bins = np.linspace(min_price, max_price, 101)
        bin_centers = (bins[:-1] + bins[1:]) / 2

        # Efficient volume contribution calculation
        volume_profile = np.zeros(len(bin_centers))

        high = df['high_price'].values
        low = df['low_price'].values
        volume = df['volume'].values

        for i in range(len(bin_centers)):
            bin_low = bins[i]
            bin_high = bins[i+1]

            overlap_mask = (low <= bin_high) & (high >= bin_low)
            candle_range = high[overlap_mask] - low[overlap_mask]
            valid = candle_range > 0
            overlap = np.minimum(
                bin_high, high[overlap_mask][valid]) - np.maximum(bin_low, low[overlap_mask][valid])
            volume_profile[i] = np.sum(
                volume[overlap_mask][valid] * (overlap / candle_range[valid]))

        # Find local maxima in volume profile
        avg_volume = df['volume'].mean()
        support_resistance_levels = []
        for i in range(1, len(volume_profile) - 1):
            if volume_profile[i] > volume_profile[i-1] and volume_profile[i] > volume_profile[i+1]:
                if volume_profile[i] > avg_volume * 1.5:
                    support_resistance_levels.append(bin_centers[i])

        # Closest support/resistance using numpy
        close_prices = df['close_price'].values
        supports = np.full_like(close_prices, np.nan, dtype=float)
        resistances = np.full_like(close_prices, np.nan, dtype=float)

        sr_array = np.array(support_resistance_levels)
        for i, price in enumerate(close_prices):
            below = sr_array[sr_array < price]
            above = sr_array[sr_array > price]
            if below.size > 0:
                supports[i] = below.max()
            else:
                supports[i] = sr_array.min()  # fallback: lowest level

            if above.size > 0:
                resistances[i] = above.min()
            else:
                resistances[i] = sr_array.max()  # fallback: highest level

        df['closest_support'] = supports
        df['closest_resistance'] = resistances

        # Pivots: vectorized using rolling windows
        df['pivot_high'] = (
            (df['high_price'].shift(1) < df['high_price']) &
            (df['high_price'].shift(2) < df['high_price']) &
            (df['high_price'].shift(-1) < df['high_price']) &
            (df['high_price'].shift(-2) < df['high_price'])
        ).astype(int)

        df['pivot_low'] = (
            (df['low_price'].shift(1) > df['low_price']) &
            (df['low_price'].shift(2) > df['low_price']) &
            (df['low_price'].shift(-1) > df['low_price']) &
            (df['low_price'].shift(-2) > df['low_price'])
        ).astype(int)

        return df

    def should_enter_trade(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series) -> bool:

        entry_signal = False

        if self.candle_counter < 800:
            self.candle_counter += 1
            return False
        self.candle_counter += 1
        if previous_candle is None or minusthree_candle is None:
            return False

        # Circuit breaker for consecutive losses
        if self.consecutive_losses >= self.max_consecutive_losses:
            if self.check_trend(candle, minusthree_candle):
                # Reset consecutive losses if trade manager and trade history are available
                if (self._trade_manager is not None and
                    hasattr(self._trade_manager, 'trade_history') and
                        self._trade_manager.trade_history is not None):
                    self._trade_manager.trade_history.consecutive_losses = 0
                self.consecutive_losses = 0
            else:
                return False

        # Cooldown period after stop loss
        if self.cooldown_candles > 0:
            self.cooldown_candles -= 1
            return False

        # if self.market_regime == "bearish":
        #    return False

        # if not self.check_trend(candle, minusthree_candle):
        #    return False

        avg_vol = candle["avg_volume_20"]

        # --- Enhanced EMA Bounce with pullback (more precise pullback measurement)
        if (previous_candle["close_price"] > previous_candle["ema50"] and
                candle["close_price"] > candle["ema50"]):

            for ema_type in ["ema50", "ema200"]:  # , "ema800"]:
                # Enhanced pullback check with precise measurement
                if (previous_candle["low_price"] <= previous_candle[ema_type] * self.pullback_threshold and
                    candle["close_price"] > candle[ema_type] and
                    # candle["body_ratio"] > self.min_body_ratio and
                        candle["rsi"] > 30):
                    entry_signal = True
                    break

        # --- Enhanced EMA Breakout with multiple confirmations
        if not entry_signal:
            if (previous_candle["close_price"] < previous_candle["ema50"] and
                candle["close_price"] > candle["ema50"] and
                candle["is_green"] and
                candle["body_ratio"] > self.min_body_ratio and
                candle["upper_wick_ratio"] < self.max_upper_wick_ratio and
                candle["volume"] > avg_vol * self.min_volume_multiplier and
                (self.is_bullish_engulfing(candle, previous_candle) or
                 self.is_hammer(candle) or
                 self.is_morning_star(candle, previous_candle, minusthree_candle))):
                entry_signal = True

        return entry_signal

    def is_bullish_engulfing(self, candle, prev_candle):
        """Identify a bullish engulfing pattern."""
        return (prev_candle["close_price"] < prev_candle["open_price"] and
                candle["close_price"] > candle["open_price"] and
                candle["open_price"] <= prev_candle["close_price"] and
                candle["close_price"] >= prev_candle["open_price"])

    def is_bearish_engulfing(self, candle: pd.Series, prev_candle: pd.Series) -> bool:
        return (prev_candle["close_price"] > prev_candle["open_price"] and
                candle["close_price"] < candle["open_price"] and
                candle["open_price"] >= prev_candle["close_price"] and
                candle["close_price"] <= prev_candle["open_price"])

    def is_hammer(self, candle):
        """Identify a hammer candlestick pattern with more precise ratio requirements."""
        body_size = abs(candle["close_price"] - candle["open_price"])
        total_size = candle["high_price"] - candle["low_price"]
        lower_wick = min(candle["open_price"],
                         candle["close_price"]) - candle["low_price"]
        upper_wick = candle["high_price"] - \
            max(candle["open_price"], candle["close_price"])

        return (body_size < 0.3 * total_size and
                lower_wick > 2 * body_size and
                upper_wick < 0.1 * total_size and
                candle["is_green"])  # Ensure it's a green hammer

    def is_morning_star(self, candle: pd.Series, prev_candle: pd.Series, minusthree_candle: pd.Series) -> bool:
        """
        Identify a morning star pattern.

        A morning star is a three-candle bullish reversal pattern consisting of:
        1. A large bearish (red) candle (minusthree_candle)
        2. A small-bodied candle (doji or spinning top) that gaps down (prev_candle)
        3. A bullish (green) candle that closes significantly into the first candle's body (candle)

        Args:
            candle: pd.Series containing current candle data (the third candle in the pattern)
            prev_candle: pd.Series containing previous candle data (the second candle in the pattern)
            minusthree_candle: pd.Series containing the candle two periods before (the first candle in the pattern)

        Returns:
            bool: True if a morning star pattern is present, False otherwise
        """

        # Get the three candles involved in the pattern
        first_candle = minusthree_candle
        middle_candle = prev_candle
        last_candle = candle

        # Check conditions:
        # 1. First candle is bearish (red) with a significant body
        first_is_bearish = not first_candle["is_green"]
        first_has_large_body = first_candle["body_ratio"] > 0.6

        # 2. Middle candle has a small body (doji or spinning top)
        middle_has_small_body = middle_candle["body_ratio"] < 0.3

        # 3. Middle candle gaps down from the first candle (traditional interpretation)
        # Note: Some interpretations allow overlap, we can make this configurable
        middle_gaps_down = middle_candle["high_price"] < first_candle["low_price"]

        # 4. Last candle is bullish (green) with a significant body
        last_is_bullish = last_candle["is_green"]
        last_has_decent_body = last_candle["body_ratio"] > 0.4

        # 5. Last candle closes significantly into the first candle's body
        # (at least 50% of the way into the first candle's body)
        first_body_low = min(
            first_candle["open_price"], first_candle["close_price"])
        first_body_high = max(
            first_candle["open_price"], first_candle["close_price"])
        first_body_size = first_body_high - first_body_low
        if first_body_size == 0:
            significant_penetration = False
        else:
            last_close_penetration = (
                last_candle["close_price"] - first_body_low) / first_body_size
            significant_penetration = last_close_penetration > 0.5

        # Check for all conditions
        return (first_is_bearish and first_has_large_body and
                middle_has_small_body and
                (middle_gaps_down or middle_candle["high_price"] < first_candle["close_price"]) and
                last_is_bullish and last_has_decent_body and
                significant_penetration)

    def should_exit_trade(self, candle: pd.Series, previous_candle: pd.Series) -> bool:
        """Enhanced exit criteria with multiple indicators."""
        # previous_candle is required by the interface but not used in this implementation
        _ = previous_candle  # Suppress unused parameter warning

        technical_breakdown = (candle["close_price"] < candle["ema800"])

        # Volume climax (potential reversal)
        volume_climax = candle["volume"] > candle["avg_volume_20"] * \
            3 and candle["close_price"] < candle["open_price"]

        # Market regime shift
        regime_shift = self.market_regime == "bearish"

        return volume_climax or regime_shift or technical_breakdown

    def check_trend(self, candle: pd.Series, minusthree_candle: pd.Series, lookback: int = 3) -> bool:

        basic_trend = (candle["close_price"] > candle["ema50"] and
                       candle["close_price"] > candle["ema200"])  # and
        # candle["close_price"] > candle["ema800"] and
        # candle["ema50"] > candle["ema200"] and
        # candle["ema200"] > candle["ema800"])

        if not basic_trend:
            return False

        ema50_slope = (candle["ema50"] - minusthree_candle["ema50"]) / lookback
        # ema200_slope = (candle["ema200"] - minusthree_candle["ema200"]) / lookback

        ema_slope_positive = ema50_slope > 0  # and ema200_slope > 0

        return ema_slope_positive

    def calculate_position_size(self, portfolio_balance: float, atr: float, atr_50: float) -> float:
        """Advanced position sizing with volatility adjustment and trade history."""
        # Base calculation from original strategy
        atr_ratio = (atr / atr_50) if atr_50 > 0 else 1

        # Adjust based on market regime
        regime_multiplier = {
            "bullish": 1.2,
            "neutral": 1.0,
            "bearish": 0.5
        }.get(self.market_regime, 1.0)

        # Adjust based on trading success rate
        if len(self.trade_results) >= 5:
            recent_results = self.trade_results[-5:]
            win_rate = sum(1 for x in recent_results if x >
                           0) / len(recent_results)
            performance_multiplier = 0.8 + win_rate * 0.4  # 0.8 to 1.2 based on win rate
        else:
            performance_multiplier = 1.0

        # Drawdown adjustment
        drawdown_multiplier = 0.8 if self.is_in_drawdown else 1.0

        # Volatility adjustment
        volatility_multiplier = 1.0
        if self.historical_volatility is not None:
            if self.historical_volatility > 1.0:  # High volatility
                volatility_multiplier = 0.8
            elif self.historical_volatility < 0.5:  # Low volatility
                volatility_multiplier = 1.1

        # Calculate final position size with all adjustments
        adjusted_percentage = self.investment_percentage * regime_multiplier * \
            performance_multiplier * drawdown_multiplier * volatility_multiplier

        investment_size = min(
            self.max_investment, adjusted_percentage * portfolio_balance * atr_ratio)
        return max(investment_size, self.min_investment)

    def get_take_profit_levels(self) -> list:
        """Dynamic take profit levels based on volatility."""
        # If high volatility, set more aggressive targets
        if self.historical_volatility is not None and self.historical_volatility > 0.8:
            return [level * 1.2 for level in self.take_profit_levels]
        return self.take_profit_levels
