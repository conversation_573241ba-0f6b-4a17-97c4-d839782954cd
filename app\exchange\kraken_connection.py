import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Set, Union
from datetime import datetime, timezone
import time

from app.exchange.kraken_websocket import KrakenWebSocketV2, ChannelType

logger = logging.getLogger(__name__)


class KrakenConnection:
    """
    Handles WebSocket connection and subscription management for Kraken.
    This class is responsible for maintaining the WebSocket connection,
    managing subscriptions, and forwarding messages to appropriate handlers.
    """

    def __init__(self, target_symbol: str = ''):
        """
        Initialize the Kraken WebSocket connection manager.

        Args:
            target_symbol: Default trading pair symbol (e.g., 'XBT/USD')
        """
        self.target_symbol = target_symbol
        self.ws = KrakenWebSocketV2(target_symbol=target_symbol)
        self._connection_event = asyncio.Event()
        self.connected = False

        # Subscription management
        self._desired_subscriptions: dict[str, dict] = {}
        self._active_subscriptions: dict[str, dict] = {}
        self._pending_subscriptions: dict[str, dict] = {}
        self._subscription_callbacks: dict[str, Callable] = {}

        # Connection state tracking
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 10
        self._backoff_factor = 2  # Exponential backoff
        self._max_backoff = 60    # Max wait time between attempts
        self._last_connection_attempt = 0
        self._connection_failures = 0

        # Register as connection listener
        self.ws.connection_listeners.add(self)

    async def connect(self, max_retries: int = 3):
        """
        Establish WebSocket connection with retry logic

        Args:
            max_retries: Maximum number of connection attempts
        """
        for attempt in range(max_retries):
            try:
                logger.info(
                    f"Attempting to connect to Kraken WebSocket (attempt {attempt + 1}/{max_retries})")

                # Check if already connected
                if self.ws.is_public_connected():
                    logger.info("Already connected to Kraken WebSocket")
                    return

                # Add timeout to connection attempt - be more generous
                await asyncio.wait_for(self.ws.connect_public(), timeout=60.0)

                # Wait for connection confirmation - allow more time for subscription setup
                if await self.wait_for_connection(timeout=30.0):
                    logger.info("Successfully connected to Kraken WebSocket")
                    return
                else:
                    logger.warning(
                        "Connection established but no confirmation received")

            except asyncio.TimeoutError:
                logger.error(f"Connection attempt {attempt + 1} timed out")
            except Exception as e:
                logger.error(f"Connection attempt {attempt + 1} failed: {e}")

                # If it's a network error or authentication issue, log more details
                if "403" in str(e) or "401" in str(e):
                    logger.error(
                        "Authentication error - check API credentials")
                elif "429" in str(e):
                    logger.error(
                        "Rate limit exceeded - waiting longer before retry")
                    await asyncio.sleep(30)  # Wait longer for rate limit
                elif "connection refused" in str(e).lower():
                    logger.error(
                        "Connection refused - check network connectivity")

            # Wait before retry with exponential backoff
            if attempt < max_retries - 1:
                wait_time = min(self._backoff_factor **
                                attempt, self._max_backoff)
                logger.info(f"Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)

        raise ConnectionError(
            f"Failed to connect after {max_retries} attempts")

    async def disconnect(self):
        """Close WebSocket connection cleanly"""
        try:
            if self.connected:
                logger.info("Disconnecting from Kraken WebSocket")

                # First, unsubscribe from all active subscriptions
                await self._cleanup_subscriptions()

                # Then disconnect
                await self.ws.close()

                # Reset state
                self.connected = False
                self._connection_event.clear()

                logger.info("Disconnected from Kraken WebSocket")
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")

    async def _cleanup_subscriptions(self):
        """Clean up all active subscriptions"""
        for sub_key in list(self._active_subscriptions.keys()):
            try:
                await self.remove_subscription(sub_key)
            except Exception as e:
                logger.error(f"Error cleaning up subscription {sub_key}: {e}")

    async def on_websocket_connected(self):
        """Handle WebSocket connection establishment"""
        self.connected = True
        self._reconnect_attempts = 0
        self._connection_failures = 0
        logger.info("WebSocket connection established and confirmed")
        self._connection_event.set()

        # Clear pending subscriptions (they're invalid now)
        self._pending_subscriptions.clear()

        # Resubscribe to everything we want
        await self._subscribe_to_desired()

    async def on_websocket_disconnected(self):
        """Handle WebSocket disconnection"""
        was_connected = self.connected
        self.connected = False
        self._connection_failures += 1

        if was_connected:
            logger.warning(
                f"WebSocket disconnected (failure #{self._connection_failures})")

        self._connection_event.clear()

        # Clear active subscriptions (they're no longer valid)
        self._active_subscriptions.clear()
        self._pending_subscriptions.clear()

        # Implement circuit breaker pattern
        if self._connection_failures >= 5:
            logger.error(
                "Too many connection failures - implementing circuit breaker")
            # Schedule circuit breaker reset without blocking
            asyncio.create_task(self._reset_circuit_breaker())

    async def _reset_circuit_breaker(self):
        """Reset circuit breaker after delay"""
        await asyncio.sleep(300)  # Wait 5 minutes
        self._connection_failures = 0
        logger.info("Circuit breaker reset - reconnection attempts allowed")

    async def add_subscription(self, sub_key: str, channel: ChannelType, symbols: list[str],
                               callback: Callable, **params) -> bool:
        """
        Add a new subscription at runtime

        Args:
            sub_key: Unique identifier for this subscription
            channel: Channel type to subscribe to
            symbols: List of symbols to subscribe to
            callback: Function to call when data is received
            **params: Additional parameters for the subscription

        Returns:
            bool: True if subscription was added successfully
        """
        sub_config = {
            'channel': channel,
            'symbols': symbols,
            'callback': callback,
            'params': params
        }

        # Add to desired subscriptions
        self._desired_subscriptions[sub_key] = sub_config

        # Subscribe immediately if connected
        if self.connected:
            await self._subscribe_single(sub_key, sub_config)

        return True

    async def remove_subscription(self, sub_key: str) -> bool:
        """
        Remove a subscription at runtime

        Args:
            sub_key: Unique identifier of the subscription to remove

        Returns:
            bool: True if subscription was removed successfully
        """
        # Remove from desired subscriptions
        self._desired_subscriptions.pop(sub_key, None)

        # Unsubscribe if active
        if sub_key in self._active_subscriptions:
            config = self._active_subscriptions.pop(sub_key)
            try:
                await self.ws.unsubscribe(
                    channel=config['channel'],
                    symbols=config['symbols']
                )
                logger.info(f"Unsubscribed from {sub_key}")
                return True
            except Exception as e:
                logger.error(f"Error unsubscribing from {sub_key}: {e}")
                return False

        # Clean up other tracking
        self._pending_subscriptions.pop(sub_key, None)
        self._subscription_callbacks.pop(sub_key, None)
        return True

    def on_subscription_confirmed(self, sub_key: str):
        """Handle subscription confirmation from WebSocket"""
        if sub_key in self._pending_subscriptions:
            # Move from pending to active
            self._active_subscriptions[sub_key] = self._pending_subscriptions.pop(
                sub_key)
            logger.info(f"Subscription confirmed for {sub_key}")

    def on_subscription_error(self, sub_key: str, error: str):
        """Handle subscription error from WebSocket"""
        if sub_key in self._pending_subscriptions:
            config = self._pending_subscriptions.pop(sub_key)
            logger.error(f"Subscription error for {sub_key}: {error}")

    async def _subscribe_to_desired(self):
        """Subscribe to all desired subscriptions that aren't already active"""
        for sub_key, sub_config in self._desired_subscriptions.items():
            if sub_key not in self._active_subscriptions and sub_key not in self._pending_subscriptions:
                await self._subscribe_single(sub_key, sub_config)

    async def _subscribe_single(self, sub_key: str, sub_config: dict):
        """Subscribe to a single channel with proper error handling"""
        try:
            # Mark as pending
            self._pending_subscriptions[sub_key] = sub_config

            # Store callback for later use
            self._subscription_callbacks[sub_key] = sub_config['callback']

            # Perform the subscription
            await self.ws.subscribe(
                channel=sub_config['channel'],
                symbols=sub_config['symbols'],
                callback=sub_config['callback'],
                **sub_config.get('params', {})
            )

            logger.info(f"Subscription request sent for {sub_key}")

        except Exception as e:
            # Remove from pending on error
            self._pending_subscriptions.pop(sub_key, None)

            if "already subscribed" in str(e).lower():
                # Mark as active if already subscribed
                self._active_subscriptions[sub_key] = sub_config
                logger.info(f"Already subscribed to {sub_key}")
            else:
                logger.error(f"Error subscribing to {sub_key}: {e}")

    async def wait_for_connection(self, timeout: float = 30.0) -> bool:
        """
        Wait for WebSocket connection to be established

        Args:
            timeout: Maximum time to wait in seconds

        Returns:
            bool: True if connected, False if timeout
        """
        try:
            await asyncio.wait_for(self._connection_event.wait(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            logger.error(f"Connection timeout after {timeout} seconds")
            return False

    def get_subscription_status(self) -> dict[str, str]:
        """
        Get current subscription status for debugging

        Returns:
            dict[str, str]: Mapping of subscription keys to their status
        """
        status = {}
        for sub_key in self._desired_subscriptions:
            if sub_key in self._active_subscriptions:
                status[sub_key] = "active"
            elif sub_key in self._pending_subscriptions:
                status[sub_key] = "pending"
            else:
                status[sub_key] = "inactive"
        return status

    def get_connection_health(self) -> dict[str, Any]:
        """
        Get detailed connection health information

        Returns:
            Dict with connection health metrics
        """
        return {
            "connected": self.connected,
            "reconnect_attempts": self._reconnect_attempts,
            "connection_failures": self._connection_failures,
            "active_subscriptions": len(self._active_subscriptions),
            "pending_subscriptions": len(self._pending_subscriptions),
            "desired_subscriptions": len(self._desired_subscriptions),
            "last_connection_attempt": self._last_connection_attempt
        }
