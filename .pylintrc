[MASTER]
# Add files or directories to the blacklist. They should be base names, not paths.
ignore=CVS,
       .git,
       __pycache__,
       .pytest_cache,
       .mypy_cache,
       .tox,
       venv,
       .venv,
       env,
       .env,
       node_modules

# Add files or directories matching the regex patterns to the blacklist. The regex
# matches against base names, not paths.
ignore-patterns=^\..*,
                .*_pb2\.py$,
                .*_pb2_grpc\.py$,
                migrations/.*\.py$

# Python code to execute, usually for sys.path manipulation such as
# pygtk.require().
#init-hook=

# Use multiple processes to speed up Pylint. Specifying 0 will auto-detect the
# number of processors available to use.
jobs=0

# Control the amount of potential inferred values when inferring a single
# object. This can help the performance when dealing with large classes or
# methods, too many potential paths in the code, or complex nested
# comprehensions.
limit-inference-results=100

# List of plugins (as comma separated values of python modules names) to load,
# usually to register additional checkers.
load-plugins=pylint.extensions.check_elif,
             pylint.extensions.bad_builtin,
             pylint.extensions.docparams,
             pylint.extensions.for_any_all,
             pylint.extensions.set_membership,
             pylint.extensions.code_style,
             pylint.extensions.typing,
             pylint.extensions.comparison_placement

# Pickle collected data for later runs.
persistent=yes

# When enabled, pylint would attempt to guess common misconfiguration and emit
# user-friendly hints instead of false-positive error messages.
suggestion-mode=yes

# Allow loading of arbitrary C extensions. Extensions are imported into the
# active Python interpreter and may run arbitrary code.
unsafe-load-any-extension=no


[MESSAGES CONTROL]
# Only show warnings with the listed confidence levels. Leave empty to show
# all. Valid levels: HIGH, CONTROL_FLOW, INFERENCE, INFERENCE_FAILURE,
# UNDEFINED, DEFAULT, HIGH
confidence=HIGH,
           CONTROL_FLOW,
           INFERENCE

# Disable the message, report, category or checker with the given id(s). You
# can either give multiple identifiers separated by comma (,) or put this
# option multiple times (only on the command line, not in the configuration
# file where it should appear only once). You can also use "--disable=all" to
# disable everything first and then re-enable specific checks. For example, if
# you want to run only the similarities checker, you can use "--disable=all
# --enable=similarities".
disable=raw-checker-failed,
        bad-inline-option,
        locally-disabled,
        file-ignored,
        suppressed-message,
        useless-suppression,
        deprecated-pragma,
        use-symbolic-message-instead,
        fixme,
        too-many-arguments,
        too-many-instance-attributes,
        too-many-locals,
        too-many-branches,
        too-many-statements,
        too-few-public-methods,
        too-many-public-methods,
        too-many-return-statements,
        too-many-nested-blocks,
        import-error,
        no-name-in-module,
        no-member,
        ungrouped-imports,
        wrong-import-order,
        c-extension-no-member,
        arguments-differ,
        signature-differs,
        abstract-method,
        unused-wildcard-import,
        wildcard-import,
        redefined-outer-name,
        global-statement,
        invalid-name,
        missing-function-docstring,
        missing-class-docstring,
        missing-module-docstring

# Enable the message, report, category or checker with the given id(s). You can
# either give multiple identifier separated by comma (,) or put this option
# multiple time (only on the command line, not in the configuration file where
# it should appear only once). See also the "--disable" option for examples.
enable=c-extension-no-member,
       useless-suppression,
       deprecated-pragma,
       use-symbolic-message-instead,
       unused-import,
       unused-variable,
       unused-argument,
       unreachable,
       duplicate-key,
       unnecessary-pass,
       unnecessary-lambda,
       dangerous-default-value,
       pointless-statement,
       pointless-string-statement,
       expression-not-assigned,
       unnecessary-comprehension,
       consider-using-enumerate,
       consider-using-dict-comprehension,
       consider-using-set-comprehension,
       consider-using-generator,
       consider-using-ternary,
       consider-using-with,
       consider-using-join,
       consider-using-in,
       consider-using-get,
       consider-iterating-dictionary,
       simplifiable-if-statement,
       simplifiable-if-expression,
       redefined-builtin,
       unused-format-string-argument,
       unused-format-string-key,
       format-combined-specification,
       missing-format-attribute,
       invalid-format-index,
       anomalous-backslash-in-string,
       anomalous-unicode-escape-in-string


[REPORTS]
# Python expression which should return a note less than 10 (10 is the highest
# note). You have access to the variables errors warning, statement which
# respectively contain the number of errors / warnings messages and the total
# number of statements analyzed. This is used by the global evaluation report
# (RP0004).
evaluation=10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

# Template used to display messages. This is a python new-style format string
# used to format the message information. See doc for all details.
msg-template={path}:{line}:{column}: [{msg_id}({symbol}), {obj}] {msg}

# Set the output format. Available formats are text, parseable, colorized, json
# and msvs (visual studio). You can also give a reporter class, e.g.
# mypackage.mymodule.MyReporterClass.
output-format=colorized

# Tells whether to display a full report or only the messages.
reports=no

# Activate the evaluation score.
score=yes


[REFACTORING]
# Maximum number of nested blocks for function / method body
max-nested-blocks=5

# Complete name of functions that never returns. When checking for
# inconsistent-return-statements if a never returning function is called then
# it will be considered as an explicit return statement and no message will be
# printed.
never-returning-functions=sys.exit,argparse.ArgumentParser.error


[BASIC]
# Naming style matching correct argument names.
argument-naming-style=snake_case

# Regular expression matching correct argument names. Overrides parameter-
# naming-style.
#argument-rgx=

# Naming style matching correct attribute names.
attr-naming-style=snake_case

# Regular expression matching correct attribute names. Overrides attr-naming-
# style.
#attr-rgx=

# Bad variable names which should always be refused, separated by a comma.
bad-names=foo,
          bar,
          baz,
          toto,
          tutu,
          tata,
          temp,
          tmp,
          data,
          info,
          obj,
          item,
          val,
          var

# Naming style matching correct class attribute names.
class-attribute-naming-style=any

# Regular expression matching correct class attribute names. Overrides class-
# attribute-naming-style.
#class-attribute-rgx=

# Naming style matching correct class names.
class-naming-style=PascalCase

# Regular expression matching correct class names. Overrides class-naming-style.
#class-rgx=

# Naming style matching correct constant names.
const-naming-style=UPPER_CASE

# Regular expression matching correct constant names. Overrides const-naming-
# style.
#const-rgx=

# Minimum line length for functions/classes that require docstrings, shorter
# ones are exempt.
docstring-min-length=10

# Naming style matching correct function names.
function-naming-style=snake_case

# Regular expression matching correct function names. Overrides function-naming-
# style.
#function-rgx=

# Good variable names which should always be accepted, separated by a comma.
good-names=i,
           j,
           k,
           ex,
           Run,
           _,
           id,
           pk,
           db,
           ui,
           ok,
           fp,
           fn,
           df,
           dt,
           ts,
           ax,
           fig,
           x,
           y,
           z,
           lr,
           cv,
           tf,
           np,
           pd,
           os,
           re,
           io,
           gc,
           fs,
           ws,
           ip,
           md,
           qa,
           qr,
           s3,
           ec2,
           rds,
           sqs,
           sns,
           api,
           url,
           uri,
           sql,
           orm,
           jwt,
           csv,
           xml,
           json,
           yaml,
           html,
           css,
           js,
           ts,
           py,
           rb,
           go,
           rs,
           cpp,
           hpp,
           exe,
           dll,
           lib,
           bin,
           etc,
           tmp,
           log,
           cfg,
           env,
           dev,
           prod,
           test,
           mock,
           fake,
           stub,
           src,
           dst,
           min,
           max,
           avg,
           sum,
           len,
           str,
           int,
           bool,
           dict,
           list,
           set,
           tuple

# Include a hint for the correct naming format with invalid-name.
include-naming-hint=yes

# Naming style matching correct inline iteration names.
inlinevar-naming-style=any

# Regular expression matching correct inline iteration names. Overrides
# inlinevar-naming-style.
#inlinevar-rgx=

# Naming style matching correct method names.
method-naming-style=snake_case

# Regular expression matching correct method names. Overrides method-naming-
# style.
#method-rgx=

# Naming style matching correct module names.
module-naming-style=snake_case

# Regular expression matching correct module names. Overrides module-naming-
# style.
#module-rgx=

# Colon-delimited sets of names that determine each other's naming style when
# the name regexes allow several styles.
name-group=

# Regular expression which should only match function or class names that do
# not require a docstring.
no-docstring-rgx=^(_|test_|Test|setUp|tearDown).*

# List of decorators that produce properties, such as abc.abstractproperty. Add
# to this list to register other decorators that produce valid properties.
# These decorators are taken in consideration only for invalid-name.
property-classes=abc.abstractproperty,
                 abc.abstractmethod,
                 dataclasses.dataclass,
                 property,
                 cached_property,
                 functools.cached_property

# Naming style matching correct variable names.
variable-naming-style=snake_case

# Regular expression matching correct variable names. Overrides variable-naming-
# style.
#variable-rgx=


[FORMAT]
# Expected format of line ending, e.g. empty (any line ending), LF or CRLF.
expected-line-ending-format=LF

# Regexp for a line that is allowed to be longer than the limit.
ignore-long-lines=^\s*(# )?<?https?://\S+>?$

# Number of spaces of indent applied inside line continuations.
indent-after-paren=4

# String used as indentation unit. This is usually "    " (4 spaces) or "\t" (1
# tab).
indent-string='    '

# Maximum number of characters in a single line.
max-line-length=120

# Maximum number of lines in a module.
max-module-lines=1500

# Allow the body of a class to be on the same line as the declaration if body
# contains single statement.
single-line-class-stmt=no

# Allow the body of an if to be on the same line as the test if there is no
# else.
single-line-if-stmt=no


[LOGGING]
# The type of string formatting that logging methods do. `old` means using %
# formatting, `new` is for `{}` formatting.
logging-format-style=new

# Logging modules to check that the string format arguments are in logging
# function parameter format.
logging-modules=logging


[MISCELLANEOUS]
# List of note tags to take in consideration, separated by a comma.
notes=FIXME,
      XXX,
      TODO,
      HACK,
      BUG,
      OPTIMIZE,
      REFACTOR


[SIMILARITIES]
# Comments are removed from the similarity computation
ignore-comments=yes

# Docstrings are removed from the similarity computation
ignore-docstrings=yes

# Imports are removed from the similarity computation
ignore-imports=yes

# Signatures are removed from the similarity computation
ignore-signatures=yes

# Minimum lines number of a similarity.
min-similarity-lines=6


[SPELLING]
# Limits count of emitted suggestions for spelling mistakes.
max-spelling-suggestions=4

# Spelling dictionary name. Available dictionaries: none. To make it working,
# install the PyEnchant package.
spelling-dict=

# List of comma separated words that should be considered directives if they
# appear at the beginning of a comment and should not be checked.
spelling-ignore-comment-directives=fmt: off,fmt: on,noqa:,noqa,nosec,isort:skip,mypy:

# List of comma separated words that should not be checked.
spelling-ignore-words=

# A path to a file that contains the private dictionary; one word per line.
spelling-private-dict-file=

# Tells whether to store unknown words to the private dictionary (see the
# --spelling-private-dict-file option) instead of raising a message.
spelling-store-unknown-words=no


[TYPECHECK]
# List of decorators that produce context managers, such as
# contextlib.contextmanager. Add to this list to register other decorators that
# produce valid context managers.
contextmanager-decorators=contextlib.contextmanager,
                          contextlib.asynccontextmanager

# List of members which are set dynamically and missed by pylint inference
# system, and so shouldn't trigger E1101 when accessed. Python regular
# expressions are accepted.
generated-members=numpy.*,
                  torch.*,
                  cv2.*,
                  requests.*,
                  django.*,
                  flask.*,
                  fastapi.*,
                  sqlalchemy.*

# Tells whether missing members accessed in mixin class should be ignored. A
# mixin class is detected if its name ends with "mixin" (case insensitive).
ignore-mixin-members=yes

# Tells whether to warn about missing members when the owner of the attribute
# is inferred to be None.
ignore-none=yes

# This flag controls whether pylint should warn about no-member and similar
# checks whenever an opaque object is returned when inferring. The inference
# can return multiple potential results while evaluating a Python object, but
# some branches might not be evaluated, which results in partial inference. In
# that case, it might be useful to still emit no-member and other checks for
# the rest of the inferred objects.
ignore-on-opaque-inference=yes

# List of class names for which member attributes should not be checked (useful
# for classes with dynamically set attributes). This supports the use of
# qualified names.
ignored-classes=optparse.Values,
                thread._local,
                _thread._local,
                argparse.Namespace,
                django.http.request.HttpRequest,
                django.http.response.HttpResponse

# List of module names for which member attributes should not be checked
# (useful for modules/projects where namespaces are manipulated during runtime
# and thus existing member attributes cannot be deduced by static analysis). It
# supports qualified module names, as well as Unix pattern matching.
ignored-modules=cv2,
                numpy,
                torch,
                tensorflow,
                keras,
                sklearn,
                scipy,
                matplotlib,
                seaborn,
                pandas,
                requests,
                urllib3,
                sqlalchemy,
                django,
                flask,
                fastapi,
                celery,
                redis,
                pymongo,
                psycopg2,
                mysql,
                sqlite3

# Show a hint with possible names when a member name was not found. The aspect
# of finding the hint is borrowed from IDLE.
missing-member-hint=yes

# The minimum edit distance for a name to be considered a similar match for
# missing member names.
missing-member-hint-distance=1

# The total number of similar names that should be taken in consideration when
# showing a hint for a missing member name.
missing-member-max-choices=3

# List of decorators that change the signature of a decorated function.
signature-mutators=click.decorators.option,
                   click.decorators.argument,
                   contextlib.contextmanager,
                   contextlib.asynccontextmanager


[VARIABLES]
# List of additional names supposed to be defined in builtins. Remember that
# you should avoid defining new builtins when possible.
additional-builtins=

# Tells whether unused global variables should be treated as a violation.
allow-global-unused-variables=yes

# List of names allowed to shadow builtins
allowed-redefined-builtins=id,
                           type,
                           input,
                           format,
                           filter,
                           map,
                           zip,
                           range,
                           list,
                           dict,
                           set,
                           tuple,
                           frozenset,
                           str,
                           int,
                           float,
                           bool,
                           bytes,
                           compile,
                           eval,
                           exec,
                           globals,
                           locals,
                           vars,
                           dir,
                           help,
                           copyright,
                           credits,
                           license,
                           quit,
                           exit

# List of strings which can identify a callback function by name. A callback
# name must start or end with one of those strings.
callbacks=cb_,
          _cb,
          callback_,
          _callback,
          handler_,
          _handler,
          listener_,
          _listener

# A regular expression matching the name of dummy variables (i.e. expected to
# not be used).
dummy-variables-rgx=^(_+[a-zA-Z0-9_]*)?$

# Argument names that match this expression will be ignored. Default to name
# with leading underscore.
ignored-argument-names=^_.*|^unused_.*|^args$|^kwargs$

# Tells whether we should check for unused import in __init__ files.
init-import=no

# List of qualified module names which can have different spellings.
redefining-builtins-modules=six.moves,
                            past.builtins,
                            future.builtins,
                            builtins,
                            io

# List of additional names expected to be defined in __init__ files.
init-hook-names=


[CLASSES]
# Warn about protected attribute access inside special methods
check-protected-access-in-special-methods=no

# List of method names used to declare (i.e. assign) instance attributes.
defining-attr-methods=__init__,
                      __new__,
                      setUp,
                      __post_init__

# List of member names, which should be excluded from the protected access
# warning.
exclude-protected=_asdict,
                  _fields,
                  _replace,
                  _source,
                  _make

# List of valid names for the first argument in a class method.
valid-classmethod-first-arg=cls

# List of valid names for the first argument in a metaclass class method.
valid-metaclass-classmethod-first-arg=cls


[DESIGN]
# List of regular expressions of class ancestor names to ignore when counting
# public methods (see R0903)
exclude-too-few-public-methods=

# List of qualified class names to ignore when counting class parents (see
# R0901)
ignored-parents=

# Maximum number of arguments for function / method.
max-args=7

# Maximum number of attributes for a class (see R0902).
max-attributes=12

# Maximum number of boolean expressions in an if statement (see R0916).
max-bool-expr=5

# Maximum number of branch for function / method body.
max-branches=15

# Maximum number of locals for function / method body.
max-locals=20

# Maximum number of parents for a class (see R0901).
max-parents=7

# Maximum number of public methods for a class (see R0904).
max-public-methods=25

# Maximum number of return statements in a function / method body.
max-returns=8

# Maximum number of statements in function / method body.
max-statements=60

# Minimum number of public methods for a class (see R0903).
min-public-methods=1


[IMPORTS]
# List of modules that can be imported at any level, not just the top level
allow-any-import-level=

# Allow wildcard imports from modules that define __all__.
allow-wildcard-with-all=no

# Analyse import fallback blocks. This can be used to support both Python 2 and
# 3 compatible code, which means that the block might have code that exists
# only in one or the other interpreter, leading to false positives when analysed.
analyse-fallback-blocks=no

# Deprecated modules which should not be used, separated by a comma.
deprecated-modules=optparse,tkinter.tix

# Create a graph of external dependencies in the given file (report RP0402 must
# not be disabled).
ext-import-graph=

# Create a graph of external dependencies (report RP0402 must not be disabled).
int-import-graph=

# Force import order to recognize a module as part of the standard
# compatibility libraries.
known-standard-library=

# Force import order to recognize a module as part of a third party library.
known-third-party=enchant

# Couples of modules and preferred modules, separated by a comma.
preferred-modules=


[EXCEPTIONS]
# Exceptions that will emit a warning when being caught. Defaults to
# "BaseException,Exception".
overgeneral-exceptions=BaseException,Exception

