"""
Comprehensive Reinforcement Learning Trading Model Training System

This module implements a systematic approach for robust PPO model training and evaluation
with proper data validation, hyperparameter optimization, and reproducibility.
"""

import os
import sys
import json
import logging
import numpy as np
import pandas as pd
import torch
import random
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict, field
from pathlib import Path

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

from stable_baselines3 import PPO
from stable_baselines3.common.callbacks import BaseCallback
from app.strategy.trading_env import TradingEnv
from app.strategy.ppo_strategy import PPOStrategy
from app.scripts.historical_data_feed import HistoricalDataFeed
from app.db.db_executor import DatabaseExecutor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class TrainingConfig:
    """Configuration for training parameters"""
    # Data configuration
    pair: str = "SOL/USD"
    start_date: datetime = datetime(2021, 6, 17, tzinfo=timezone.utc)
    end_date: datetime = datetime(2025, 7, 10, tzinfo=timezone.utc)

    # Training splits
    train_ratio: float = 0.7
    validation_ratio: float = 0.2
    test_ratio: float = 0.1

    # PPO hyperparameters
    learning_rate_initial: float = 0.0003
    learning_rate_final: float = 0.00005
    n_steps: int = 1024
    batch_size: int = 128
    n_epochs: int = 10
    gamma: float = 0.97
    gae_lambda: float = 0.95
    clip_range: float = 0.2
    ent_coef_initial: float = 0.05
    ent_coef_final: float = 0.01
    vf_coef: float = 0.5
    max_grad_norm: float = 0.5

    # Training configuration
    total_timesteps: int = 100000
    random_seeds: List[int] = field(default_factory=lambda: [42, 123, 456, 789, 999])

    # Evaluation configuration
    evaluation_frequency: int = 10000
    save_frequency: int = 25000

    def __post_init__(self):
        # Validate batch_size divides n_steps
        if self.n_steps % self.batch_size != 0:
            raise ValueError(f"batch_size ({self.batch_size}) must divide n_steps ({self.n_steps})")


@dataclass
class EvaluationMetrics:
    """Metrics for model evaluation"""
    total_return: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_drawdown: float
    volatility: float
    win_rate: float
    avg_trade_duration: float
    trade_frequency: float
    var_95: float
    final_net_worth: float

    def to_dict(self) -> Dict[str, float]:
        return asdict(self)


@dataclass
class ExperimentResult:
    """Results from a single experiment"""
    config: TrainingConfig
    seed: int
    metrics: EvaluationMetrics
    model_path: str
    training_time: float

    def to_dict(self) -> Dict[str, Any]:
        return {
            'config': asdict(self.config),
            'seed': self.seed,
            'metrics': self.metrics.to_dict(),
            'model_path': self.model_path,
            'training_time': self.training_time
        }


class TrainingCallback(BaseCallback):
    """Custom callback for monitoring training progress"""

    def __init__(self, eval_env: TradingEnv, eval_freq: int = 10000, verbose: int = 1):
        super().__init__(verbose)
        self.eval_env = eval_env
        self.eval_freq = eval_freq
        self.evaluations = []

    def _on_step(self) -> bool:
        if self.n_calls % self.eval_freq == 0:
            # Evaluate model
            obs, _ = self.eval_env.reset()
            done = False
            total_reward = 0

            while not done:
                action, _ = self.model.predict(obs, deterministic=True)
                obs, reward, terminated, truncated, _ = self.eval_env.step(action)
                done = terminated or truncated
                total_reward += reward

            self.evaluations.append({
                'timestep': self.n_calls,
                'total_reward': total_reward,
                'final_net_worth': self.eval_env.portfolio_manager.get_current_total_value(
                    self.eval_env.current_price
                )
            })

            if self.verbose > 0:
                logger.info(f"Evaluation at step {self.n_calls}: "
                          f"Total reward: {total_reward:.4f}, "
                          f"Net worth: ${self.evaluations[-1]['final_net_worth']:.2f}")

        return True


class ComprehensiveTrainingSystem:
    """Main training system implementing all recommended practices"""

    def __init__(self, db: DatabaseExecutor, output_dir: str = "./training_results"):
        self.db = db
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories
        (self.output_dir / "models").mkdir(exist_ok=True)
        (self.output_dir / "logs").mkdir(exist_ok=True)
        (self.output_dir / "results").mkdir(exist_ok=True)

    def set_seeds(self, seed: int) -> None:
        """Set all random seeds for reproducibility"""
        np.random.seed(seed)
        torch.manual_seed(seed)
        random.seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)

        # Set deterministic behavior
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

    def linear_schedule(self, initial_value: float, final_value: float):
        """Create a linear learning rate schedule"""
        def schedule(progress_remaining: float) -> float:
            return initial_value + (final_value - initial_value) * (1 - progress_remaining)
        return schedule

    def entropy_schedule(self, initial_value: float, final_value: float):
        """Create an entropy coefficient decay schedule"""
        def schedule(progress_remaining: float) -> float:
            return initial_value + (final_value - initial_value) * (1 - progress_remaining)
        return schedule

    async def load_and_split_data(self, config: TrainingConfig) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load data and create chronological train/validation/test splits"""
        logger.info(f"Loading data for {config.pair} from {config.start_date} to {config.end_date}")

        # Create PPO strategy for preprocessing
        ppo_strategy = PPOStrategy(pair=config.pair)

        # Create historical data feed with order book features
        async def ob_fetcher(start_time, end_time):
            query = """
            SELECT pair, snapshot_time, bids, asks, checksum
            FROM order_book_snapshots
            WHERE pair = %s AND snapshot_time BETWEEN %s AND %s
            ORDER BY snapshot_time ASC
            """
            results = self.db.execute_select(query, (config.pair, start_time, end_time))

            if not results:
                return pd.DataFrame()

            feature_records = []
            for row in results:
                try:
                    bids = json.loads(row[2]) if isinstance(row[2], str) else row[2]
                    asks = json.loads(row[3]) if isinstance(row[3], str) else row[3]

                    orderbook_data = {
                        'pair': row[0],
                        'snapshot_time': row[1],
                        'bids': bids or [],
                        'asks': asks or [],
                        'checksum': row[4]
                    }

                    ob_features = ppo_strategy.calculate_orderbook_features(orderbook_data)
                    ob_features['timestamp'] = row[1]
                    feature_records.append(ob_features)
                except Exception as e:
                    logger.warning(f"Error processing order book snapshot: {e}")
                    continue

            return pd.DataFrame(feature_records)

        data_feed = HistoricalDataFeed(
            db=self.db,
            pair=config.pair,
            start_date=config.start_date,
            end_date=config.end_date,
            preprocess_func=ppo_strategy.preprocess_data,
            order_book_fetcher=ob_fetcher
        )

        await data_feed.connect()

        if data_feed.candles is None or data_feed.candles.empty:
            raise ValueError(f"No data found for {config.pair}")

        df = data_feed.candles.copy()
        logger.info(f"Loaded {len(df)} candles with columns: {list(df.columns)}")

        # Create chronological splits
        total_len = len(df)
        train_end = int(total_len * config.train_ratio)
        val_end = int(total_len * (config.train_ratio + config.validation_ratio))

        train_df = df.iloc[:train_end].copy()
        val_df = df.iloc[train_end:val_end].copy()
        test_df = df.iloc[val_end:].copy()

        logger.info(f"Data splits - Train: {len(train_df)}, Validation: {len(val_df)}, Test: {len(test_df)}")
        logger.info(f"Train period: {train_df['timestamp'].iloc[0]} to {train_df['timestamp'].iloc[-1]}")
        logger.info(f"Validation period: {val_df['timestamp'].iloc[0]} to {val_df['timestamp'].iloc[-1]}")
        logger.info(f"Test period: {test_df['timestamp'].iloc[0]} to {test_df['timestamp'].iloc[-1]}")

        return train_df, val_df, test_df

    def calculate_evaluation_metrics(self, env: TradingEnv) -> EvaluationMetrics:
        """Calculate comprehensive evaluation metrics"""
        net_worth_series = pd.Series(env.net_worth_history)
        returns = net_worth_series.pct_change().dropna()

        # Basic metrics
        total_return = (net_worth_series.iloc[-1] / net_worth_series.iloc[0] - 1) * 100
        volatility = returns.std() * np.sqrt(252) * 100  # Annualized

        # Risk-adjusted metrics
        sharpe_ratio = returns.mean() / (returns.std() + 1e-9) * np.sqrt(252)

        # Sortino ratio (downside deviation)
        downside_returns = returns[returns < 0]
        sortino_ratio = returns.mean() / (downside_returns.std() + 1e-9) * np.sqrt(252)

        # Maximum drawdown
        cumulative = net_worth_series / net_worth_series.cummax()
        max_drawdown = (1 - cumulative.min()) * 100

        # Calmar ratio
        calmar_ratio = total_return / (max_drawdown + 1e-9)

        # Trading metrics
        trades = getattr(env.trade_manager, 'completed_trades', [])
        if trades:
            winning_trades = [t for t in trades if t.get('profit', 0) > 0]
            win_rate = len(winning_trades) / len(trades) * 100

            # Average trade duration (in hours)
            durations = []
            for trade in trades:
                if 'entry_time' in trade and 'exit_time' in trade:
                    duration = (trade['exit_time'] - trade['entry_time']).total_seconds() / 3600
                    durations.append(duration)
            avg_trade_duration = np.mean(durations) if durations else 0

            trade_frequency = len(trades) / len(env.net_worth_history) * 24  # Trades per day
        else:
            win_rate = 0
            avg_trade_duration = 0
            trade_frequency = 0

        # Value at Risk (95%)
        var_95 = np.percentile(returns, 5) * 100 if len(returns) > 0 else 0

        return EvaluationMetrics(
            total_return=float(total_return),
            sharpe_ratio=float(sharpe_ratio),
            sortino_ratio=float(sortino_ratio),
            calmar_ratio=float(calmar_ratio),
            max_drawdown=float(max_drawdown),
            volatility=float(volatility),
            win_rate=float(win_rate),
            avg_trade_duration=float(avg_trade_duration),
            trade_frequency=float(trade_frequency),
            var_95=float(var_95),
            final_net_worth=float(net_worth_series.iloc[-1])
        )

    def train_single_model(self, config: TrainingConfig, train_df: pd.DataFrame,
                          val_df: pd.DataFrame, seed: int) -> Tuple[PPO, str, float]:
        """Train a single model with given configuration and seed"""
        import time
        start_time = time.time()

        # Set seeds for reproducibility
        self.set_seeds(seed)

        # Create environments
        strategy = PPOStrategy(pair=config.pair)
        train_env = TradingEnv(train_df, strategy)
        val_env = TradingEnv(val_df, strategy)

        # Create model with hyperparameters
        model = PPO(
            "MlpPolicy",
            train_env,
            learning_rate=self.linear_schedule(config.learning_rate_initial, config.learning_rate_final),
            n_steps=config.n_steps,
            batch_size=config.batch_size,
            n_epochs=config.n_epochs,
            gamma=config.gamma,
            gae_lambda=config.gae_lambda,
            clip_range=config.clip_range,
            ent_coef=config.ent_coef_initial,  # Note: PPO doesn't support schedules for ent_coef directly
            vf_coef=config.vf_coef,
            max_grad_norm=config.max_grad_norm,
            verbose=1,
            tensorboard_log=str(self.output_dir / "logs" / f"seed_{seed}")
        )

        # Create callback for evaluation
        callback = TrainingCallback(val_env, config.evaluation_frequency)

        # Train model
        logger.info(f"Training model with seed {seed} for {config.total_timesteps} timesteps")
        model.learn(
            total_timesteps=config.total_timesteps,
            callback=callback,
            progress_bar=True
        )

        # Save model
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_filename = f"ppo_{config.pair.replace('/', '')}_{timestamp}_seed_{seed}.zip"
        model_path = str(self.output_dir / "models" / model_filename)
        model.save(model_path)

        training_time = time.time() - start_time
        logger.info(f"Training completed in {training_time:.2f} seconds. Model saved to {model_path}")

        return model, model_path, training_time

    def evaluate_model(self, model: PPO, test_df: pd.DataFrame, config: TrainingConfig) -> EvaluationMetrics:
        """Evaluate model on test data"""
        strategy = PPOStrategy(pair=config.pair)
        test_env = TradingEnv(test_df, strategy)

        obs, _ = test_env.reset()
        done = False

        while not done:
            action_array, _ = model.predict(obs, deterministic=True)
            # Convert action array to integer
            action = int(action_array.item()) if hasattr(action_array, 'item') else int(action_array)
            obs, reward, terminated, truncated, _ = test_env.step(action)
            done = terminated or truncated

        return self.calculate_evaluation_metrics(test_env)

    async def run_experiment(self, config: TrainingConfig) -> List[ExperimentResult]:
        """Run complete experiment with multiple seeds"""
        logger.info(f"Starting experiment with {len(config.random_seeds)} seeds")

        # Load and split data
        train_df, val_df, test_df = await self.load_and_split_data(config)

        results = []

        for seed in config.random_seeds:
            logger.info(f"Running experiment with seed {seed}")

            try:
                # Train model
                model, model_path, training_time = self.train_single_model(
                    config, train_df, val_df, seed
                )

                # Evaluate model
                metrics = self.evaluate_model(model, test_df, config)

                # Create result
                result = ExperimentResult(
                    config=config,
                    seed=seed,
                    metrics=metrics,
                    model_path=model_path,
                    training_time=training_time
                )

                results.append(result)

                logger.info(f"Seed {seed} completed - Sharpe: {metrics.sharpe_ratio:.4f}, "
                          f"Return: {metrics.total_return:.2f}%, "
                          f"Max DD: {metrics.max_drawdown:.2f}%")

            except Exception as e:
                logger.error(f"Error training model with seed {seed}: {e}")
                continue

        # Save results
        self.save_experiment_results(results, config)

        return results

    def save_experiment_results(self, results: List[ExperimentResult], config: TrainingConfig):
        """Save experiment results to JSON file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = self.output_dir / "results" / f"experiment_{timestamp}.json"

        # Convert results to serializable format
        serializable_results = [result.to_dict() for result in results]

        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)

        logger.info(f"Experiment results saved to {results_file}")

        # Print summary statistics
        if results:
            metrics_df = pd.DataFrame([result.metrics.to_dict() for result in results])

            logger.info("\n" + "="*50)
            logger.info("EXPERIMENT SUMMARY")
            logger.info("="*50)
            logger.info(f"Number of runs: {len(results)}")
            logger.info(f"Configuration: {config.pair}")
            logger.info("\nPerformance Metrics (Mean ± Std):")

            for metric in ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']:
                mean_val = metrics_df[metric].mean()
                std_val = metrics_df[metric].std()
                logger.info(f"  {metric}: {mean_val:.4f} ± {std_val:.4f}")

            # Best performing model
            best_idx = int(metrics_df['sharpe_ratio'].idxmax())
            best_result = results[best_idx]
            logger.info(f"\nBest model (Seed {best_result.seed}):")
            logger.info(f"  Path: {best_result.model_path}")
            logger.info(f"  Sharpe Ratio: {best_result.metrics.sharpe_ratio:.4f}")
            logger.info(f"  Total Return: {best_result.metrics.total_return:.2f}%")
            logger.info(f"  Max Drawdown: {best_result.metrics.max_drawdown:.2f}%")
            logger.info("="*50)
