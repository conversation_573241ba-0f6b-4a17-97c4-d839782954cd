from db.db_executor import DatabaseExecutor
import os
import sys
import logging
from os import getenv
from datetime import datetime, timezone, timedelta
import requests
from ksLib.environment import Environment
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def fetch_binance_data(symbol, start_time, end_time):
    # Binance API URL for historical candlestick data
    url = "https://api.binance.com/api/v3/klines"
    params = {
        "symbol": symbol,
        "interval": "1m",  # 1-minute interval
        "startTime": int(start_time.timestamp() * 1000),
        "endTime": int(end_time.timestamp() * 1000),
        "limit": 1000  # Max number of candles to return
    }

    response = requests.get(url, params=params)
    candles_data = response.json()

    # Format the data for output
    binance_data = []
    for candle in candles_data:
        timestamp = datetime.fromtimestamp(candle[0] / 1000, tz=timezone.utc)
        open_price = float(candle[1])
        high_price = float(candle[2])
        low_price = float(candle[3])
        close_price = float(candle[4])
        volume = float(candle[5])
        binance_data.append(
            (timestamp, open_price, high_price, low_price, close_price, volume))

    return binance_data


def get_first_ohlc_timestamp(db: DatabaseExecutor, pair: str):
    query = """
    SELECT MIN(timestamp)
    FROM binance_ohlc
    WHERE pair = %s
    """
    result = db.execute_select(query, (pair,))
    if result:
        # First column of the first row (earliest timestamp)
        return result[0][0]
    return None


def get_max_timestamp(db: DatabaseExecutor, pair: str) -> datetime:
    query = "SELECT MAX(timestamp) FROM binance_ohlc WHERE pair = %s;"
    result = db.execute_select(query, (pair,))
    max_timestamp = result[0][0] if result and result[0][0] else datetime.min.replace(
        tzinfo=timezone.utc)
    return max_timestamp.replace(tzinfo=timezone.utc)  # Ensure timezone-aware


def insert_ohlc_from_binance(pair: str, db: DatabaseExecutor, start_date: datetime, end_date: datetime):
    max_timestamp = get_max_timestamp(db, pair)
    logger.info(f"Max timestamp in database for {pair}: {max_timestamp}")

    insert_query = (
        "INSERT INTO binance_ohlc (timestamp, open_price, high_price, "
        "low_price, close_price, volume, pair) "
        "VALUES (%s, %s, %s, %s, %s, %s, %s) "
        "ON CONFLICT (timestamp, pair) DO NOTHING;"
    )

    current_start = max(start_date, max_timestamp + timedelta(minutes=1))

    rows_inserted = 0
    rows_skipped = 0

    while current_start < end_date:
        current_end = min(current_start + timedelta(days=1), end_date)

        # Fetch data in segments to handle the 1000 data limit
        while current_start < current_end:
            binance_data = fetch_binance_data(
                pair, current_start, current_start + timedelta(minutes=1000))

            for row in binance_data:
                timestamp = row[0]
                if timestamp <= max_timestamp:
                    rows_skipped += 1
                    continue

                affected_rows = db.execute_insert(insert_query, (*row, pair))
                rows_inserted += affected_rows

            # Move the start time forward to cover the next batch of data
            # Adjust this based on the actual limit you want to fetch
            current_start += timedelta(minutes=1000)

        current_start = current_end  # Move to the next day's start

    logger.info(f"Inserted {rows_inserted} new rows for {pair}")
    logger.info(
        f"Skipped {rows_skipped} rows (already in database or older than max timestamp)")


def fill_missing_ohlc_data(db: DatabaseExecutor, pair: str, start_date: datetime, end_date: datetime):
    logger.info(
        f"Filling missing OHLC data for {pair} from {start_date} to {end_date}")

    query = """
    SELECT timestamp, open_price, high_price, low_price, close_price, volume
    FROM binance_ohlc
    WHERE pair = %s AND timestamp BETWEEN %s AND %s
    ORDER BY timestamp
    """
    existing_data = db.execute_select(query, (pair, start_date, end_date))

    if not existing_data:
        logger.warning(
            f"No existing data found for {pair} in the specified date range.")
        return

    existing_data_dict = {row[0]: row[1:] for row in existing_data}

    current_time = start_date
    last_known_time = None
    missing_periods = []
    interpolated_data = []

    while current_time <= end_date:
        if current_time not in existing_data_dict:
            # If the current time is missing
            if last_known_time is not None and (current_time - last_known_time).total_seconds() > 60:
                # If there's a gap greater than 1 minute
                if not missing_periods or missing_periods[-1][1] is not None:
                    # Start a new missing period
                    missing_periods.append([current_time, None])
        else:
            # If we have a known time, update the last known time
            last_known_time = current_time

            # Close the last missing period if it's still open
            if missing_periods and missing_periods[-1][1] is None:
                # Close the last missing period to the previous timestamp
                missing_periods[-1][1] = current_time - timedelta(minutes=1)

        current_time += timedelta(minutes=1)

    # Close any open missing period at the end
    if missing_periods and missing_periods[-1][1] is None:
        missing_periods[-1][1] = end_date

    # Filter out periods with None as the end time
    missing_periods = [
        period for period in missing_periods if period[1] is not None]

    # Try to fetch missing data from API first
    fetched_data_to_insert = []
    for start, end in missing_periods:
        try:
            fetched_data = fetch_binance_data(pair, start, end)
            if fetched_data:
                for row in fetched_data:
                    timestamp, open_price, high_price, low_price, close_price, volume = row
                    existing_data_dict[timestamp] = (
                        open_price, high_price, low_price, close_price, volume)
                    fetched_data_to_insert.append((*row, pair))
                logger.info(
                    f"Fetched {len(fetched_data)} missing data points from API for period {start} to {end}")
            else:
                logger.info(
                    f"No data available from API for period {start} to {end}")
        except Exception as e:
            logger.error(
                f"Error fetching data from API for period {start} to {end}: {str(e)}")

    # Insert fetched data into the database
    if fetched_data_to_insert:
        insert_query = """
        INSERT INTO binance_ohlc (timestamp, open_price, high_price, low_price, close_price, volume, pair)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (timestamp, pair) DO NOTHING
        """
        db.execute_insert_many(insert_query, fetched_data_to_insert)
        logger.info(
            f"Inserted {len(fetched_data_to_insert)} fetched data points into the database for {pair}")

    # Interpolate any remaining missing data
    current_time = start_date
    last_known_data = None

    while current_time <= end_date:
        if current_time not in existing_data_dict:
            if last_known_data is not None:
                # Last known close becomes new open
                interpolated_open = last_known_data[3]
                interpolated_close = interpolated_open
                interpolated_high = interpolated_open
                interpolated_low = interpolated_open
                interpolated_volume = 0

                next_time = current_time + timedelta(minutes=1)
                while next_time not in existing_data_dict and next_time <= end_date:
                    next_time += timedelta(minutes=1)

                if next_time in existing_data_dict:
                    next_data = existing_data_dict[next_time]
                    interpolated_close = (interpolated_open + next_data[3]) / 2
                    interpolated_high = max(interpolated_high, next_data[1])
                    interpolated_low = min(interpolated_low, next_data[2])
                    # Calculate time difference in seconds and convert to minutes
                    # Calculate time difference in seconds and convert to minutes
                    time_diff_seconds = (
                        next_time.timestamp() - last_known_data[0].timestamp())
                    time_diff_minutes = time_diff_seconds / 60
                    # Handle volume calculation safely
                    try:
                        # Ensure we're working with a numeric value for volume
                        if isinstance(last_known_data[4], (int, float, str)):
                            # Index 4 is volume in the tuple
                            last_volume = float(last_known_data[4])
                            interpolated_volume = last_volume / \
                                time_diff_minutes if time_diff_minutes > 0 else 0
                        else:
                            interpolated_volume = 0
                    except (TypeError, ValueError, IndexError):
                        # Fallback if there's any issue with the conversion
                        interpolated_volume = 0

                interpolated_data.append((
                    current_time, interpolated_open, interpolated_high,
                    interpolated_low, interpolated_close, interpolated_volume, pair
                ))
        else:
            last_known_data = (current_time,) + \
                existing_data_dict[current_time]

        current_time += timedelta(minutes=1)

    if interpolated_data:
        insert_query = """
        INSERT INTO binance_ohlc (timestamp, open_price, high_price, low_price, close_price, volume, pair)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (timestamp, pair) DO NOTHING
        """
        db.execute_insert_many(insert_query, interpolated_data)
        logger.info(
            f"Inserted {len(interpolated_data)} interpolated data points for {pair}")
    else:
        logger.info(f"No missing data points to interpolate for {pair}")


if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    e = Environment()
    e.load_generic_environment_files()
    env = getenv("ENVIRONMENT")

    # PostgreSQL connection details
    DB_NAME = getenv('DB_NAME')
    DB_USER = getenv('DB_USER')
    DB_PASSWORD = getenv('DB_PASSWORD')
    DB_HOST = getenv('DB_HOST')
    DB_PORT = getenv('DB_PORT')

    db = DatabaseExecutor(DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT)
    pair = 'SOLUSDC'  # Binance uses USDC instead of USD

    try:
        end_date = datetime.now(timezone.utc).replace(
            hour=0, minute=0, second=0, microsecond=0)
        # Fetch last 1219 days of data
        start_date = end_date - timedelta(days=1219)

        first_timestamp = get_first_ohlc_timestamp(db, pair)
        if first_timestamp:
            start_date = min(start_date, first_timestamp)

        insert_ohlc_from_binance(pair, db, start_date, end_date)
        logger.info('Binance OHLC data import completed successfully!')

        fill_missing_ohlc_data(db, pair, start_date, end_date)
        logger.info('Missing OHLC data filled successfully!')
    except Exception as e:
        logger.error(f"An error occurred during data import: {str(e)}")
    finally:
        db.close()
