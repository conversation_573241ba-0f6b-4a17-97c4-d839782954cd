from typing import Optional
from datetime import datetime
from dataclasses import dataclass


@dataclass
class Trade:
    """Data class to store individual trade information."""
    trade_id: str
    trade_id_match: str
    entry_price: float
    current_price: float
    entry_time: datetime
    pair: str
    volume: float
    initial_volume: float  # Store the initial volume for partial exits
    remaining_volume: float  # Track remaining volume after partial exits
    entry_fee: float  # Added fee field
    sell_fee: float  # Added fee field
    strategy_name: str
    entry_reason: str
    partial_exits: list[dict]  # Store information about partial exits
    reached_profit_levels: list[bool]
    trailing_stop_on: bool
    max_price: float
    trailing_price: float = 0.0
    exit_price: Optional[float] = None
    exit_timestamp: Optional[datetime] = None
    profit: Optional[float] = None

    @property
    def unrealized_pnl(self) -> float:
        """Calculate current unrealized PnL."""
        return (self.current_price - self.entry_price) * self.remaining_volume - self.entry_fee

    def close_trade(self, exit_price: float, exit_timestamp: datetime):
        self.exit_price = exit_price
        self.exit_timestamp = exit_timestamp
        self.profit = (self.exit_price - self.entry_price) * \
            self.volume - self.entry_fee - self.sell_fee
