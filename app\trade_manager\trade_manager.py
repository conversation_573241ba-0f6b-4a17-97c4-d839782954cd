import logging
import pandas as pd
from typing import Optional
from datetime import datetime, timezone
from uuid import uuid4
from app.trade_history.trade_history import TradeHistory
from app.trade_history.trade_action import TradeAction
from .trade import Trade
from .portfolio_manager import PortfolioManager
from .result import Result
from app.scripts.base_order_executor import OrderExecutor
from app.exchange.kraken_api import KrakenAPI

# Configure logging
logger = logging.getLogger(__name__)


class TradeManager:
    def __init__(self, portfolio_manager: PortfolioManager, order_executor: Optional[OrderExecutor], trade_history: Optional[TradeHistory], api: Optional[KrakenAPI], mode: str = 'backtest'):
        logger.info(f"Initializing TradeManager with mode: {mode}")
        self.portfolio_manager = portfolio_manager
        self.order_executor = order_executor  # For placing orders in live mode
        self.mode = mode  # 'backtest', 'paper', or 'live'
        self.active_trades: dict[str, Trade] = {}
        # Track pending orders in live mode
        self.pending_orders: dict[str, dict] = {}
        self.trade_history = trade_history
        self.api = api  # Store API reference even if not used directly

    def open_trade(self, price: float, volume: float, timestamp: datetime,
                   pair: str, fee: float, strategy_name: str, entry_reason: str, take_profit_levels: list[float]) -> Optional[str]:
        logger.info(
            f"Attempting to open trade: pair={pair}, volume={volume}, strategy={strategy_name}, mode={self.mode}")
        if self.mode in ['backtest', 'paper']:
            return self._open_trade_immediate(price, volume, timestamp, pair, fee, strategy_name, entry_reason, take_profit_levels)
        elif self.mode == 'live':
            return self._place_buy_order(price, volume, timestamp, pair, fee, strategy_name, entry_reason, take_profit_levels)
        else:
            logger.error(f"Unknown trading mode: {self.mode}")
            raise ValueError(f"Unknown mode: {self.mode}")

    def _open_trade_immediate(self, price: float, volume: float, timestamp: datetime,
                              pair: str, fee: float, strategy_name: str, entry_reason: str, take_profit_levels: list[float]) -> Optional[str]:
        trade_id = str(uuid4())
        trade_id_match = f"{strategy_name}_buy_{str(uuid4())}"
        trade = Trade(
            trade_id=trade_id,
            trade_id_match=trade_id_match,
            entry_price=price,
            current_price=price,
            entry_time=timestamp,
            pair=pair,
            volume=volume,
            initial_volume=volume,
            remaining_volume=volume,
            entry_fee=fee,  # Explicitly track entry fee
            sell_fee=0,     # Initialize sell fee
            strategy_name=strategy_name,
            entry_reason=entry_reason,
            partial_exits=[],
            reached_profit_levels=[False] * len(take_profit_levels),
            trailing_stop_on=False,
            trailing_price=0,
            max_price=price
        )
        if self.portfolio_manager.reserve_funds(price * volume):
            self.active_trades[trade_id] = trade
            logger.info(
                f"Trade opened: trade_id={trade_id}, pair={pair}, volume={volume}, strategy={strategy_name}")
            if self.trade_history:
                self.trade_history.log_action(
                    trade_id=trade_id,
                    trade_id_match=trade_id_match,
                    timestamp=timestamp,
                    action_type='buy',
                    price=price,
                    volume=volume,
                    fee=fee,
                    reason=entry_reason,
                    strategy_name=strategy_name,
                    pair=pair
                )
            return trade_id
        logger.warning(
            f"Failed to reserve funds for trade: pair={pair}, volume={volume}")
        return None

    def complete_exit(self, trade_id: str, exit_price: float, timestamp: datetime,
                      fee: float, exit_reason: str) -> None:
        logger.info(
            f"Attempting complete exit: trade_id={trade_id}, exit_price={exit_price}, reason={exit_reason}")
        if self.mode in ['backtest', 'paper']:
            self._complete_exit_immediate(
                trade_id, exit_price, timestamp, fee, exit_reason)
        elif self.mode == 'live':
            self._place_sell_order(
                trade_id, exit_price, timestamp, fee, exit_reason, is_partial=False)

    def _complete_exit_immediate(self, trade_id: str, exit_price: float, timestamp: datetime,
                                 fee: float, exit_reason: str) -> None:
        if trade_id not in self.active_trades:
            logger.error(f"Trade {trade_id} not found in active trades")
            raise ValueError(f"Trade {trade_id} not found in active trades")
        trade = self.active_trades[trade_id]
        sell_trade_id_match = f"{trade.strategy_name}_sell_{str(uuid4())}"
        remaining_volume = trade.remaining_volume

        # Update the trade with sell fee
        trade.sell_fee = fee

        # Calculate total fees (entry and exit)
        total_fees = trade.entry_fee + trade.sell_fee

        # Improved PnL calculation:
        # PnL = (Exit Price - Entry Price) * Volume - Total Fees
        pnl = round((exit_price - trade.entry_price)
                    * remaining_volume - total_fees, 2)

        logger.info(
            f"Completing trade exit: trade_id={trade_id}, pnl={pnl}, volume={remaining_volume}, total_fees={total_fees}")
        if self.trade_history:
            self.trade_history.log_action(
                trade_id=trade_id,
                trade_id_match=sell_trade_id_match,
                timestamp=timestamp,
                action_type='sell',
                price=exit_price,
                volume=remaining_volume,
                fee=fee,
                reason=exit_reason,
                strategy_name=trade.strategy_name,
                pair=trade.pair
            )
            self.trade_history.log_result(
                strategy_name=trade.strategy_name,
                entry_trade_id_match=trade.trade_id_match,
                exit_trade_id_match=sell_trade_id_match,
                entry_price=trade.entry_price,
                exit_price=exit_price,
                entry_timestamp=trade.entry_time,
                exit_timestamp=timestamp,
                volume=remaining_volume,
                entry_fee=trade.entry_fee,
                exit_fee=trade.sell_fee,
                profit_loss=pnl,
                duration=(
                    (timestamp.replace(tzinfo=timezone.utc) if timestamp.tzinfo is None else timestamp) -
                    (trade.entry_time.replace(tzinfo=timezone.utc)
                     if trade.entry_time.tzinfo is None else trade.entry_time.astimezone(timezone.utc))
                ),
                success=pnl > 0,
                exit_reason=exit_reason
            )
        self.portfolio_manager.add_realized_pnl(pnl)
        self.portfolio_manager.release_funds(
            trade.entry_price * remaining_volume)
        trade.close_trade(exit_price, timestamp)
        del self.active_trades[trade_id]

    # Similar modifications should be made to partial_exit and _partial_exit_immediate methods
    def partial_exit(self, trade_id: str, exit_price: float, exit_volume: float,
                     timestamp: datetime, fee: float, exit_reason: str) -> None:
        logger.info(
            f"Attempting partial exit: trade_id={trade_id}, exit_price={exit_price}, exit_volume={exit_volume}")
        if self.mode in ['backtest', 'paper']:
            self._partial_exit_immediate(
                trade_id, exit_price, exit_volume, timestamp, fee, exit_reason)
        elif self.mode == 'live':
            self._place_sell_order(trade_id, exit_price, timestamp, fee,
                                   exit_reason, is_partial=True, exit_volume=exit_volume)

    def _partial_exit_immediate(self, trade_id: str, exit_price: float, exit_volume: float,
                                timestamp: datetime, fee: float, exit_reason: str) -> None:
        if trade_id not in self.active_trades:
            logger.error(f"Trade {trade_id} not found in active trades")
            raise ValueError(f"Trade {trade_id} not found in active trades")

        trade = self.active_trades[trade_id]

        if exit_volume > trade.remaining_volume:
            logger.error(
                f"Exit volume {exit_volume} exceeds remaining volume {trade.remaining_volume}")
            raise ValueError(
                f"Exit volume {exit_volume} exceeds remaining volume {trade.remaining_volume}")

        # Calculate the proportion of the trade being exited
        exit_proportion = exit_volume / trade.remaining_volume

        sell_trade_id_match = f"{trade.strategy_name}_sell_{str(uuid4())}"

        # Proportional fee calculation
        # This assumes the fee should be proportional to the exited volume
        proportional_entry_fee = trade.entry_fee * exit_proportion

        # Calculate PnL for this partial exit
        # Subtract the proportional entry fee and the specific exit fee
        partial_pnl = round(
            (exit_price - trade.entry_price) * exit_volume -
            proportional_entry_fee -
            fee,
            2
        )

        logger.info(
            f"Processing partial exit: trade_id={trade_id}, partial_pnl={partial_pnl}, exit_volume={exit_volume}")

        # Log the sell action
        if self.trade_history:
            self.trade_history.log_action(
                trade_id=trade_id,
                trade_id_match=sell_trade_id_match,
                timestamp=timestamp,
                action_type='sell',
                price=exit_price,
                volume=exit_volume,
                fee=fee,
                reason=exit_reason,
                strategy_name=trade.strategy_name,
                pair=trade.pair
            )

        # Update trade's total fees and remaining volume
        trade.entry_fee = round(trade.entry_fee - proportional_entry_fee, 5)
        trade.remaining_volume -= exit_volume
        trade.current_price = exit_price

        # Log the trade result
        if self.trade_history:
            self.trade_history.log_result(
                strategy_name=trade.strategy_name,
                entry_trade_id_match=trade.trade_id_match,
                exit_trade_id_match=sell_trade_id_match,
                entry_price=trade.entry_price,
                exit_price=exit_price,
                entry_timestamp=trade.entry_time,
                exit_timestamp=timestamp,
                volume=exit_volume,
                entry_fee=proportional_entry_fee,  # Proportional entry fee for this partial exit
                exit_fee=fee,                      # Specific exit fee
                profit_loss=partial_pnl,
                duration=timestamp - trade.entry_time,
                success=partial_pnl > 0,
                exit_reason=exit_reason
            )

        # Update portfolio
        self.portfolio_manager.add_realized_pnl(partial_pnl)
        self.portfolio_manager.release_funds(trade.entry_price * exit_volume)

        # Check if trade is fully closed
        if trade.remaining_volume <= 0:
            logger.info(
                f"Trade fully closed after partial exit: trade_id={trade_id}")
            del self.active_trades[trade_id]

    def _place_buy_order(self, price: float, volume: float, timestamp: datetime,
                         pair: str, fee: float, strategy_name: str, entry_reason: str, take_profit_levels: list[float]) -> str:
        """Place a buy order in live mode and track it as pending."""
        if not self.order_executor:
            logger.error("Cannot place buy order: order_executor is None")
            raise ValueError("order_executor is required for placing orders")

        order_response = self.order_executor.place_order(
            order_type='buy',
            price=price,
            volume=volume,
            timestamp=timestamp,
            pair=pair
        )
        if order_response.get('status') == 'pending':
            order_id = order_response['txid']
            self.pending_orders[order_id] = {
                'type': 'buy',
                'price': price,
                'volume': volume,
                'timestamp': timestamp,
                'pair': pair,
                'fee': fee,
                'strategy_name': strategy_name,
                'entry_reason': entry_reason,
                'take_profit_levels': take_profit_levels
            }
            return order_id
        raise Exception(f"Failed to place buy order: {order_response}")

    def _place_sell_order(self, trade_id: str, exit_price: float, timestamp: datetime,
                          fee: float, exit_reason: str, is_partial: bool, exit_volume: Optional[float] = None) -> None:
        if trade_id not in self.active_trades:
            logger.error(f"Trade {trade_id} not found in active trades")
            raise ValueError(f"Trade {trade_id} not found in active trades")

        if not self.order_executor:
            logger.error("Cannot place sell order: order_executor is None")
            raise ValueError("order_executor is required for placing orders")

        trade = self.active_trades[trade_id]
        volume_to_sell = exit_volume if is_partial else trade.remaining_volume

        if volume_to_sell is None:
            logger.error(f"Invalid volume to sell: {volume_to_sell}")
            raise ValueError("Volume to sell cannot be None")

        logger.info(
            f"Placing sell order: trade_id={trade_id}, volume={volume_to_sell}, is_partial={is_partial}")
        order_response = self.order_executor.place_order(
            order_type='sell',
            price=exit_price,
            volume=volume_to_sell,
            timestamp=timestamp,
            pair=trade.pair
        )
        if order_response.get('status') == 'pending':
            order_id = order_response['txid']
            self.pending_orders[order_id] = {
                'type': 'sell',
                'trade_id': trade_id,
                'price': exit_price,
                'volume': volume_to_sell,
                'timestamp': timestamp,
                'fee': fee,
                'exit_reason': exit_reason,
                'is_partial': is_partial
            }
            logger.info(
                f"Sell order placed: order_id={order_id}, trade_id={trade_id}")
        else:
            logger.error(f"Failed to place sell order: {order_response}")
            raise Exception(f"Failed to place sell order: {order_response}")

    def process_filled_orders(self, filled_orders: list[dict]) -> None:
        """Process filled orders in live mode and update active trades."""
        for order in filled_orders:
            order_id = order['txid']
            if order_id in self.pending_orders:
                pending_order = self.pending_orders.pop(order_id)
                if pending_order['type'] == 'buy':
                    self._create_trade_from_filled_buy(order, pending_order)
                elif pending_order['type'] == 'sell':
                    self._process_filled_sell(order, pending_order)

    def _create_trade_from_filled_buy(self, order: dict, pending_order: dict) -> None:
        """Create a trade from a filled buy order in live mode, using executed volume."""
        trade_id = order['txid']
        trade_id_match = f"{pending_order['strategy_name']}_buy_{str(uuid4())}"
        filled_price = order['price']
        filled_volume = order.get('vol_exec', order.get(
            'volume', 0))  # Use executed volume
        if filled_volume == 0:
            logger.warning(f"Order {order['txid']} has zero executed volume.")
            return
        filled_fee = order['fee']

        trade = Trade(
            trade_id=trade_id,
            trade_id_match=trade_id_match,
            entry_price=filled_price,
            current_price=filled_price,
            entry_time=order['timestamp'],
            pair=pending_order['pair'],
            volume=filled_volume,
            initial_volume=filled_volume,
            remaining_volume=filled_volume,
            entry_fee=filled_fee,
            sell_fee=0,
            strategy_name=pending_order['strategy_name'],
            entry_reason=pending_order['entry_reason'],
            partial_exits=[],
            reached_profit_levels=[False] *
            len(pending_order['take_profit_levels']),
            trailing_stop_on=False,
            trailing_price=0,
            max_price=filled_price
        )

        if self.portfolio_manager.reserve_funds(filled_price * filled_volume):
            self.active_trades[trade_id] = trade
            logger.info(
                f"Trade opened: trade_id={trade_id}, pair={trade.pair}, volume={trade.volume}, strategy={trade.strategy_name}")
            if self.trade_history:
                self.trade_history.log_action(
                    trade_id=trade_id,
                    trade_id_match=trade_id_match,
                    timestamp=order['timestamp'],
                    action_type='buy',
                    price=filled_price,
                    volume=filled_volume,
                    fee=filled_fee,
                    reason=pending_order['entry_reason'],
                    strategy_name=pending_order['strategy_name'],
                    pair=pending_order['pair']
                )
            else:
                logger.warning(
                    "No trade history available, skipping log_action for buy")

    def _process_filled_sell(self, order: dict, pending_order: dict) -> None:
        """Process a filled sell order in live mode, using executed volume."""
        trade_id = pending_order['trade_id']
        if trade_id not in self.active_trades:
            print(f"Warning: Trade {trade_id} not found for filled sell order")
            return
        trade = self.active_trades[trade_id]
        filled_price = order['price']
        filled_volume = order.get('vol_exec', order.get(
            'volume', 0))  # Use executed volume
        if filled_volume == 0:
            logger.warning(f"Order {order['txid']} has zero executed volume.")
            return
        filled_fee = order['fee']
        exit_reason = pending_order['exit_reason']
        sell_trade_id_match = f"{trade.strategy_name}_sell_{str(uuid4())}"

        exit_proportion = filled_volume / \
            trade.remaining_volume if trade.remaining_volume else 1.0
        proportional_entry_fee = trade.entry_fee * exit_proportion

        pnl = round(
            (filled_price - trade.entry_price) * filled_volume -
            proportional_entry_fee -
            filled_fee,
            2
        )

        logger.info(
            f"Processing exit: trade_id={trade_id}, partial_pnl={pnl}, exit_volume={filled_volume}")

        if self.trade_history:
            self.trade_history.log_action(
                trade_id=trade_id,
                trade_id_match=sell_trade_id_match,
                timestamp=order['timestamp'],
                action_type='sell',
                price=filled_price,
                volume=filled_volume,
                fee=filled_fee,
                reason=exit_reason,
                strategy_name=trade.strategy_name,
                pair=trade.pair
            )
            self.trade_history.log_result(
                strategy_name=trade.strategy_name,
                entry_trade_id_match=trade.trade_id_match,
                exit_trade_id_match=sell_trade_id_match,
                entry_price=trade.entry_price,
                exit_price=filled_price,
                entry_timestamp=trade.entry_time,
                exit_timestamp=order['timestamp'],
                entry_fee=proportional_entry_fee,
                exit_fee=filled_fee,
                volume=filled_volume,
                profit_loss=pnl,
                duration=(
                    (order['timestamp'].replace(tzinfo=timezone.utc) if order['timestamp'].tzinfo is None
                     else order['timestamp'].astimezone(timezone.utc)) -
                    (trade.entry_time.replace(tzinfo=timezone.utc) if trade.entry_time.tzinfo is None
                     else trade.entry_time.astimezone(timezone.utc))
                ),
                success=pnl > 0,
                exit_reason=exit_reason
            )
        else:
            logger.warning(
                "No trade history available, skipping log_action and log_result for sell")
        trade.remaining_volume -= filled_volume
        trade.current_price = filled_price
        trade.entry_fee = round(trade.entry_fee - proportional_entry_fee, 5)
        self.portfolio_manager.add_realized_pnl(pnl)
        self.portfolio_manager.release_funds(trade.entry_price * filled_volume)
        if trade.remaining_volume <= 0:
            del self.active_trades[trade_id]

    def get_trade_actions(self) -> list[TradeAction]:
        logger.info("Retrieving trade actions")
        if self.trade_history:
            return self.trade_history.get_trade_actions()
        logger.warning("No trade history available, returning empty list")
        return []

    def get_trade_results(self) -> list[Result]:
        logger.info("Retrieving trade results")
        if self.trade_history:
            return self.trade_history.get_trade_results()
        logger.warning("No trade history available, returning empty list")
        return []

    def process_pending_orders(self, filled_orders: list[dict]) -> None:
        # logger.info(f"Processing {len(filled_orders)} pending orders that have been filled")
        # Delegate to the existing method
        self.process_filled_orders(filled_orders)

    def sync_open_trades(self, db, take_profit_levels: list[bool]):
        logging.info("Fetching open trades from database...")
        query = """
            SELECT
                t.trade_id,
                t.trade_id_match,
                t.price AS entry_price,
                (t.volume - COALESCE(r.sold_volume, 0)) AS remaining_volume,
                (t.fee - COALESCE(r.sold_fee, 0)) AS entry_fee,
                t.timestamp AS entry_time,
                t.pair,
                t.buy_sell,
                t.reason,
                r.strategy_name
            FROM live_trades t
            LEFT JOIN (
                SELECT entry_trade_id, strategy_name, SUM(volume) AS sold_volume, SUM(entry_fee) AS sold_fee
                FROM live_results
                GROUP BY entry_trade_id, strategy_name
            ) r ON t.trade_id_match = r.entry_trade_id
            WHERE t.buy_sell = 'buy'
            AND (t.volume - COALESCE(r.sold_volume, 0)) > 0
        """
        df = pd.DataFrame(db.execute_select(query, float_columns=[2, 3, 4]), columns=[
            'trade_id', 'trade_id_match', 'entry_price', 'remaining_volume', 'entry_fee',
            'entry_time', 'pair', 'buy_sell', 'entry_reason', 'strategy_name'
        ]
        )
        open_trades = df.to_dict('records')

        if not open_trades:
            logging.info("No open trades found in database.")
            return

        verified_trades = {}
        for trade in open_trades:
            trade_id = trade['trade_id']
            if self.is_trade_still_open(trade_id):
                trade_obj = Trade(
                    trade_id=trade_id,  # No trade_id from DB, use match ID
                    trade_id_match=trade['trade_id_match'],
                    entry_price=trade['entry_price'],
                    current_price=trade['entry_price'],  # Assume unchanged
                    entry_time=trade['entry_time'],
                    pair=trade['pair'],
                    volume=trade['remaining_volume'],
                    initial_volume=trade['remaining_volume'],
                    remaining_volume=trade['remaining_volume'],
                    entry_fee=trade['entry_fee'],
                    sell_fee=0,
                    strategy_name=trade['strategy_name'],
                    entry_reason=trade['entry_reason'],
                    partial_exits=[],
                    reached_profit_levels=[False] * len(take_profit_levels),
                    trailing_stop_on=False,
                    trailing_price=0,
                    max_price=trade['entry_price']
                )
                verified_trades[trade_id] = trade_obj

        self.active_trades = verified_trades
        logging.info(f"Synced {len(self.active_trades)} open trades.")

    def is_trade_still_open(self, _: str) -> bool:
        """
        Verify with Kraken if the trade is still open.

        Returns:
            bool: True if the trade is still open, False otherwise
        """
        return True  # Because open positions are only for future market not SPOT market
        # Unreachable code commented out to avoid warnings
        # if self.api:
        #     positions = self.api.get_open_positions()
        #     self.open_positions = positions['result'] if positions.get('result') else {}
        #     for pos_tx in self.open_positions:
        #         pos = self.open_positions[pos_tx]
        #     if pos:
        #         return True
        # return False
