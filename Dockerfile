FROM python:3.13-slim-bookworm

RUN apt-get update && apt-get install -y \
    build-essential wget tar \
    libbz2-dev liblzma-dev \
    python3-dev \
  && rm -rf /var/lib/apt/lists/*

# Download and install TA-Lib C library v0.6.4 from source release
RUN wget https://github.com/TA-Lib/ta-lib/releases/download/v0.6.4/ta-lib-0.6.4-src.tar.gz \
  && tar -xzf ta-lib-0.6.4-src.tar.gz \
  && cd ta-lib-0.6.4 \
  && ./configure --prefix=/usr \
  && make && make install \
  && ldconfig

# Install the Python wrapper
RUN pip install --upgrade pip setuptools numpy \
  && pip install TA-Lib

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    USER=app

ENV HOME=/home/<USER>
    PATH=/usr/local/bin:$PATH

# Create a non-root user and set permissions
RUN adduser --uid 2000 --gecos "" --disabled-password $USER \
    && mkdir -p $HOME/scripts \
    && chown -R $USER:$USER $HOME

# Switch to non-root user
USER $USER

# Copy requirements and install dependencies
COPY ./app/requirements.txt ./
RUN python -m pip install --upgrade pip \
    && pip install -U --no-cache-dir --no-warn-script-location -r requirements.txt

# Set the working directory
WORKDIR $HOME/scripts/

# Set default command to run the application and redirect logs to file
CMD ["sh", "-c", "python main.py > /home/<USER>/scripts/logs/app.log 2>&1"]
