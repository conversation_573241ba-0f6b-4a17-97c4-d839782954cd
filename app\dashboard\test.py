import time

class TradingMetricsRunner:
    def __init__(self, pair, mode, currency, initial_portfolio):
        self.pair = pair
        self.mode = mode
        self.currency = currency
        self.initial_portfolio = initial_portfolio

    def run(self):
        from data_fetcher import fetch_metrics
        while True:
            metrics = fetch_metrics(self.pair, self.mode, self.currency, self.initial_portfolio)
            print("Trading Metrics:")
            for key, value in metrics.items():
                print(f"{key}: {value}")
            time.sleep(60)  # Run every 60 seconds

if __name__ == "__main__":
    runner = TradingMetricsRunner(pair='SOLUSD', mode='live', currency='USD', initial_portfolio=5000)
    runner.run()
