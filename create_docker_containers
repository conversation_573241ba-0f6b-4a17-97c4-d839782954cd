docker run -d --name=telegraf --network=telegraph_network -v /Users/<USER>/Library/CloudStorage/OneDrive-Kolektor/Flexibility\ platform/Software/Setup.peak/SCADA/ua/telegraf.conf:/etc/telegraf/telegraf.conf telegraf

docker run -d --name=acroni-peak -p 8125:8125 acroni-peak

az login

az acr login --name kolektorsetup

docker build -t acroni-peak .

docker build -f Dockerfile.dev -t acroni-peak .

docker buildx build --platform linux/amd64 -t acroni-peak .
 
docker tag sha256:78354afbc74be4a77d732fce9cc0d1eea2dae62d70f51b1ab52f6936c64dcb22 kolektorsetup.azurecr.io/fsi/acroni-peak:acroni-peak-0.0.1

docker push kolektorsetup.azurecr.io/fsi/acroni-peak:acroni-peak-0.0.1


az aks get-credentials --resource-group setup-flexibility --name flexibility-dev

az aks get-credentials --resource-group setup-flexibility --name flexibility-prod


az login --tenant 8541dae1-904b-433f-a318-030780dbfe80

kubectl config set-context --current --namespace=fsi

kubectl describe telegraf-deployment-frr-mo-5456996577-gcmc5 -n fsi 

kubectl logs acroni-peak-5b696f9789-2q74w -c acroni-peak -n fsi 


kubectl apply -f deployment-acroni-peak.yaml -n fsi

kubectl delete -f deployment-acroni-peak.yaml -n fsi

kubectl delete pod telegraf-deployment-frr-mo-55cc5bc889-ct7nx -n fsi

kubectl rollout restart deploy/acroni-peak -n fsi

Monitor:

kubectl get pods -n fsi

docker-compose up db

//Session            Local Time                   DST OFF (UCT+0)DST ON (UTC+0)DST ON 2022  DST OFF 2022DST ON 2023  DST OFF 2023  DST ON 2024DST OFF 2024
//London            8am-430pm                   0800-1630    0700-1530    March, 27  October, 30March, 26  October, 29  March, 31    October, 27
//NewYork            930am-4pm                   1430-2100    1330-2000    March, 13  November, 6March, 12  November, 5  March, 10    November, 3
//Tokyo                9am-3pm                       0000-0600    0000-0600    N/A          N/A        N/A          N/A          N/A        N/A
//HongKong            930am-4pm                   0130-0800    0130-0800    N/A          N/A        N/A          N/A          N/A        N/A
//Sydney (NZX+ASX)    NZX start 10am, ASX end 4pm   2200-0600    2100-0500    October, 2  April, 3    October, 1  April, 2      October, 6April, 7
//EU Brinx            800am-900am                   0800-0900    0700-0800    March, 27  October, 30March, 26  October, 29  March, 31    October, 27
//US Brinx            900am-10am                   1400-1500    1300-1400    March, 13  November, 6March, 12  November, 5  March, 10    November, 3
//Frankfurt            800am-530pm                   0700-1630    0600-1530    March, 27  October, 30March, 26  October, 29  March, 31    October, 27
 