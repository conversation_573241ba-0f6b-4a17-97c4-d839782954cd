import pandas as pd
import numpy as np
import logging
from app.db.db_executor import DatabaseExecutor
from typing import Dict

# These imports are commented out but kept for future use
# import seaborn as sns
# import plotly.io as pio
# import plotly.graph_objects as go
# import matplotlib.pyplot as plt


class BacktestAnalyzer:

    def analyze_results(self, db_executor: DatabaseExecutor, strategy_name: str, initial_portfolio: float, pair: str):
        query = """
        SELECT sum(fee) FROM backtest_trades WHERE pair = %s
        """
        results = db_executor.execute_select(query, (pair,), float_columns=[0])
        total_fees = results[0][0] if results else 0.0

        query = """
        SELECT * FROM backtest_results WHERE strategy_name = %s
        """
        results = db_executor.execute_select(
            query, (strategy_name,), float_columns=[4, 5, 6, 7, 10, 11])
        df_basic = pd.DataFrame(results, columns=['id', 'strategy_name', 'entry_trade_id', 'exit_trade_id', 'entry_price',
                                            'exit_price', 'entry_fee','exit_fee', 'entry_timestamp', 'exit_timestamp',
                                            'volume', 'profit_loss', 'duration', 'success', 'exit_reason'])

        time_analysis = self.analyze_time_patterns(df_basic)
        # Prepare a dictionary to accumulate trade results
        trade_results = {}

        # Aggregate results for each `entry_trade_id`
        for _, row in df_basic.iterrows():
            entry_trade_id = row['entry_trade_id']

            # If this entry_trade_id is not in the results dictionary, initialize it
            if entry_trade_id not in trade_results:
                min_price = float('inf')
                max_price = float('-inf')

                trade_results[entry_trade_id] = {
                    'total_profit_loss': 0,
                    'total_volume': 0,
                    'total_duration': pd.Timedelta(0),
                    'success': 0,
                    'entry_price': row['entry_price'],
                    'min_exit_price': min(min_price, row['entry_price'], row['exit_price']),
                    'max_exit_price': max(max_price, row['entry_price'], row['exit_price']),
                    'entry_timestamp': row['entry_timestamp'],
                    'exit_timestamp': row['exit_timestamp'],
                    'profits': []  # Store profit/loss for each partial exit
                }

            # Update the trade result with the current exit
            trade_results[entry_trade_id]['total_profit_loss'] += row['profit_loss']
            trade_results[entry_trade_id]['total_volume'] += row['volume']
            trade_results[entry_trade_id]['total_duration'] += row['duration']
            trade_results[entry_trade_id]['exit_timestamp'] = max(trade_results[entry_trade_id]['exit_timestamp'], row['exit_timestamp'])

            # Track success for the entire trade, based on whether the final profit is positive
            if row['profit_loss'] > 0:
                trade_results[entry_trade_id]['success'] += 1

            # Store the individual profit for each partial exit
            trade_results[entry_trade_id]['profits'].append(row['profit_loss'])

        # Prepare a list to hold the aggregated rows for the new DataFrame
        aggregated_rows = []

        # Now we will create a new DataFrame with the aggregated data
        for entry_trade_id, trade in trade_results.items():
            # Aggregate the values into a row with the same columns as the original DataFrame
            aggregated_row = {
                'id': entry_trade_id,
                'strategy_name': df_basic['strategy_name'].iloc[0],
                'entry_trade_id': entry_trade_id,
                'exit_trade_id': None,
                'entry_price': trade['entry_price'],
                'min_exit_price': trade['min_exit_price'],
                'max_exit_price': trade['max_exit_price'],
                'entry_timestamp': trade['entry_timestamp'],
                'exit_timestamp': trade['exit_timestamp'],
                'volume': trade['total_volume'],
                'profit_loss': trade['total_profit_loss'],
                'duration': trade['total_duration'],
                'success': 1 if trade['total_profit_loss'] > 0 else 0,
                'exit_reason': None
            }
            aggregated_rows.append(aggregated_row)

        # Create the new DataFrame with the aggregated rows
        df = pd.DataFrame(aggregated_rows, columns=['id', 'strategy_name', 'entry_trade_id', 'exit_trade_id',
                                                            'entry_price', 'min_exit_price', 'max_exit_price', 'entry_timestamp', 'exit_timestamp',
                                                            'volume', 'profit_loss', 'duration', 'success', 'exit_reason'])

        risk_analysis = self.monte_carlo_simulation(df)
        self.log_backtest_metrics(
        {
            'time_analysis': time_analysis,
            'risk_analysis': risk_analysis
        })
        total_trades = len(df)
        winning_trades = df['success'].sum()
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades * 100
        total_profit = df['profit_loss'].sum()
        final_portfolio = initial_portfolio + total_profit
        total_return = (final_portfolio - initial_portfolio) / \
            initial_portfolio * 100
        avg_profit_per_trade = total_profit / total_trades
        max_profit = df['profit_loss'].max()
        max_loss = df['profit_loss'].min()
        avg_trade_duration = df['duration'].mean()

        trading_periods_per_day = 60 * 24
        # Sharpe Ratio calculation with error handling for zero standard deviation
        profit_loss_std = np.std(df['profit_loss'])
        if profit_loss_std != 0:
            sharpe_ratio = np.mean(df['profit_loss']) / profit_loss_std * \
                np.sqrt(trading_periods_per_day)  # Assuming daily trades
        else:
            sharpe_ratio = 0.0

        # Cumulative Profit Calculation
        df['cumulative_profit'] = df['profit_loss'].cumsum()

        # Max Drawdown Calculation
        running_max = df['cumulative_profit'].cummax()
        # drawdown = (df['cumulative_profit'] - running_max) / running_max
        # xmax_drawdown = abs(drawdown.min())

        # Portfolio Value over time
        df['portfolio_value'] = initial_portfolio + df['cumulative_profit']
        running_max_portfolio = df['portfolio_value'].cummax()
        df['drawdown'] = (df['portfolio_value'] -
                          running_max_portfolio) / running_max_portfolio
        max_drawdown = abs(df['drawdown'].min())

        drawdown_dollars = df['cumulative_profit'] - running_max
        max_drawdown_dollars = abs(drawdown_dollars.min())

        winning_trades_cash = df[df['profit_loss'] > 0]['profit_loss']
        losing_trades_cash = df[df['profit_loss'] < 0]['profit_loss']

        average_win = winning_trades_cash.mean() if len(winning_trades_cash) > 0 else 0
        average_loss = losing_trades_cash.mean() if len(losing_trades_cash) > 0 else 0

        # Sortino Ratio calculation (downside risk)
        downside_std = np.std(df[df['profit_loss'] < 0]['profit_loss'])
        if downside_std != 0:
            sortino_ratio = np.mean(
                df['profit_loss']) / downside_std * np.sqrt(trading_periods_per_day)
        else:
            sortino_ratio = 0.0

        # Profit Factor calculation
        gross_profit = df[df['profit_loss'] > 0]['profit_loss'].sum()
        gross_loss = df[df['profit_loss'] < 0]['profit_loss'].sum()
        if gross_loss != 0:
            profit_factor = gross_profit / abs(gross_loss)
        else:
            profit_factor = float('inf')

        max_duration = df['duration'].max()
        min_duration = df['duration'].min()

        # Calmar Ratio
        try:
            annual_return = (final_portfolio / initial_portfolio) ** (365 /
                                                                    (df['exit_timestamp'].max() - df['exit_timestamp'].min()).days) - 1
            calmar_ratio = annual_return / \
                max_drawdown if max_drawdown != 0 else float('inf')
        except ZeroDivisionError:
            calmar_ratio = 0

        # MAE (Maximum Adverse Excursion) and MFE (Maximum Favorable Excursion)
        df['MAE'] = df.apply(lambda row: min(row['entry_price'], row['min_exit_price']) - row['entry_price'], axis=1)
        df['MFE'] = df.apply(lambda row: max(row['entry_price'], row['max_exit_price']) - row['entry_price'], axis=1)
        # Risk-Reward Ratio
        avg_win = df[df['profit_loss'] > 0]['profit_loss'].mean()
        avg_loss = abs(df[df['profit_loss'] < 0]['profit_loss'].mean())
        risk_reward_ratio = avg_win / \
            avg_loss if avg_loss != 0 else float('inf')

        # Expectancy
        expectancy = (win_rate / 100 * avg_win) - \
            ((1 - win_rate / 100) * avg_loss)

        # Profit per day
        trading_days = (df['exit_timestamp'].max() -
                        df['exit_timestamp'].min()).days
        profit_per_day = total_profit / trading_days if trading_days > 0 else 0

        # CAGR Calculation
        start_date = df['entry_timestamp'].min()
        end_date = df['exit_timestamp'].max()
        days = (end_date - start_date).days
        years = days / 365.25
        if years >= 1:
            cagr = (final_portfolio / initial_portfolio) ** (1 / years) - 1
        else:
            # For periods less than a year, we calculate the CAGR as if it were annualized
            cagr = (final_portfolio / initial_portfolio) ** (float(365.25) / days) - 1

        # Annualized Volatility (assuming daily returns)
        # Calculate returns per trade
        df['trade_returns'] = df['profit_loss'] / initial_portfolio

        # Estimate annualized volatility using trade returns
        trade_volatility = df['trade_returns'].std()
        estimated_trades_per_year = total_trades / years
        annualized_volatility = trade_volatility * \
            np.sqrt(estimated_trades_per_year)

        print("-------------------------------------------------------------------------------------")
        print(f"Strategy: {strategy_name}")
        print(f"Initial Portfolio: ${initial_portfolio:.2f}")
        print(f"Final Portfolio: ${final_portfolio:.2f}")
        print(f"Total Return: {total_return:.2f}%")
        print(f"Total trades: {total_trades}")
        print(f"Total fees for {pair}: ${total_fees:,.2f}")
        print(f"Winning trades: {winning_trades}")
        print(f"Losing trades: {losing_trades}")
        print(f"Win rate: {win_rate:.2f}%")
        print(f"Winning Trades Cash: {winning_trades_cash.sum():.2f}")
        print(f"Losing Trades Cash: {losing_trades_cash.sum():.2f}")
        print(f"Profit Factor: {profit_factor:.2f}")
        print(f"Average Win: {average_win:.2f}")
        print(f"Average Loss: {average_loss:.2f}")
        print(f"Total profit: ${total_profit:.2f}")
        print(f"CAGR (Compound Annual Growth Rate): {cagr:.2%}")
        print(f"Annualized Volatility: {annualized_volatility:.2%}")
        print(f"Average profit per trade: ${avg_profit_per_trade:.2f}")
        print(f"Max profit: ${max_profit:.2f}")
        print(f"Max loss: ${max_loss:.2f}")
        print(f"Average trade duration: {avg_trade_duration}")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"Maximum Drawdown: {max_drawdown:.2%}")
        print(f"Maximum Drawdown in USD: ${max_drawdown_dollars:.2f}")
        print(f"Sortino Ratio: {sortino_ratio:.2f}")
        print(f"Calmar Ratio: {calmar_ratio:.2f}")
        print(f"Risk-Reward Ratio: {risk_reward_ratio:.2f}")
        print(f"Expectancy: ${expectancy:.2f}")
        print(f"Profit per day: ${profit_per_day:.2f}")
        print(f"Average MAE (Maximum Adverse Excursion): ${
              df['MAE'].mean():.2f}")
        print(f"Average MFE (Maximum Favorable Excursion): ${
              df['MFE'].mean():.2f}")
        print(f"Max trade duration: {
              max_duration}, Min trade duration: {min_duration}")
        print("-------------------------------------------------------------------------------------")

        # plt.figure(figsize=(12, 6))
        # plt.plot(df['exit_timestamp'], initial_portfolio +
        #          df['cumulative_profit'], label='Portfolio Value')
        # plt.fill_between(df['exit_timestamp'], initial_portfolio + df['cumulative_profit'],
        #                  initial_portfolio + running_max, color='red', alpha=0.3, label='Drawdown')
        # plt.title(
        #     f"Portfolio Value Over Time with Drawdowns - {strategy_name}")
        # plt.xlabel("Date")
        # plt.ylabel("Portfolio Value ($)")
        # plt.grid(True)
        # plt.legend()
        # plt.savefig(f"{strategy_name}_portfolio_value_with_drawdown.png")
        # plt.close()

        # plt.figure(figsize=(12, 6))
        # sns.histplot(df['profit_loss'], bins=50, kde=True)
        # plt.title(f"Profit Distribution with KDE - {strategy_name}")
        # plt.xlabel("Profit/Loss ($)")
        # plt.ylabel("Frequency")
        # plt.grid(True)
        # plt.savefig(f"{strategy_name}_profit_distribution_kde.png")
        # plt.close()

        # plt.figure(figsize=(12, 6))
        # plt.scatter(df['exit_timestamp'], df['profit_loss'],
        #             c=np.where(df['profit_loss'] > 0, 'green', 'red'))
        # plt.title(f"Trade Outcomes Over Time - {strategy_name}")
        # plt.xlabel("Date")
        # plt.ylabel("Profit/Loss ($)")
        # plt.grid(True)
        # plt.savefig(f"{strategy_name}_trade_outcomes.png")
        # plt.close()

        # plt.figure(figsize=(6, 6))
        # labels = ['Winning Trades', 'Losing Trades']
        # sizes = [winning_trades, losing_trades]
        # colors = ['green', 'red']
        # plt.pie(sizes, labels=labels, colors=colors,
        #         autopct='%1.1f%%', startangle=140)
        # plt.title(f"Win/Loss Ratio - {strategy_name}")
        # plt.savefig(f"{strategy_name}_win_loss_ratio.png")
        # plt.close()

        # # Underwater Plot
        # plt.figure(figsize=(12, 6))
        # underwater = (df['cumulative_profit'].cummax(
        # ) - df['cumulative_profit']) / df['cumulative_profit'].cummax()
        # plt.plot(df['exit_timestamp'], underwater)
        # plt.title(f"Underwater Plot - {strategy_name}")
        # plt.xlabel("Date")
        # plt.ylabel("Drawdown (%)")
        # plt.fill_between(df['exit_timestamp'], underwater, 0, alpha=0.3)
        # plt.grid(True)
        # plt.savefig(f"{strategy_name}_underwater_plot.png")
        # plt.close()

        # # MAE vs MFE Scatter Plot
        # plt.figure(figsize=(10, 10))
        # plt.scatter(df['MAE'], df['MFE'], alpha=0.5)
        # plt.title(f"MAE vs MFE - {strategy_name}")
        # plt.xlabel("Maximum Adverse Excursion")
        # plt.ylabel("Maximum Favorable Excursion")
        # plt.grid(True)
        # plt.savefig(f"{strategy_name}_mae_mfe_scatter.png")
        # plt.close()

        # # Rolling Sharpe Ratio
        # window = 30  # 30-day rolling window
        # rolling_sharpe = df['profit_loss'].rolling(window).apply(lambda x: np.sqrt(
        #     trading_periods_per_day) * x.mean() / x.std() if x.std() != 0 else 0)
        # plt.figure(figsize=(12, 6))
        # plt.plot(df['exit_timestamp'][window-1:], rolling_sharpe[window-1:])
        # plt.title(f"Rolling {window}-day Sharpe Ratio - {strategy_name}")
        # plt.xlabel("Date")
        # plt.ylabel("Sharpe Ratio")
        # plt.grid(True)
        # plt.savefig(f"{strategy_name}_rolling_sharpe.png")
        # plt.close()

        # # Trade Duration vs Profit/Loss
        # plt.figure(figsize=(12, 6))
        # # Convert duration to seconds for plotting
        # duration_seconds = df['duration'].dt.total_seconds()
        # plt.scatter(duration_seconds, df['profit_loss'], alpha=0.5)
        # plt.title(f"Trade Duration vs Profit/Loss - {strategy_name}")
        # plt.xlabel("Trade Duration (seconds)")
        # plt.ylabel("Profit/Loss ($)")
        # plt.grid(True)

        # # Optional: Format x-axis labels to show hours if durations are long
        # if duration_seconds.max() > 3600:  # If max duration is more than an hour
        #     plt.xlabel("Trade Duration (hours)")
        #     plt.xticks(ticks=plt.xticks()[0], labels=[
        #                f"{x/3600:.1f}" for x in plt.xticks()[0]])

        # plt.savefig(f"{strategy_name}_duration_vs_profit.png")
        # plt.close()

        # # Monthly Returns Heatmap
        # plt.figure(figsize=(12, 8))
        # # Convert to datetime and extract year and month, ignoring timezone
        # df['date'] = pd.to_datetime(df['exit_timestamp']).dt.tz_localize(None)
        # df['year'] = df['date'].dt.year
        # df['month'] = df['date'].dt.month
        # # Calculate monthly returns
        # monthly_returns = df.groupby(['year', 'month'])[
        #     'profit_loss'].sum() / initial_portfolio
        # # Reshape data for heatmap
        # heatmap_data = monthly_returns.unstack(level='month')

        # # Create heatmap
        # sns.heatmap(heatmap_data, cmap='RdYlGn',
        #             center=0, annot=True, fmt='.2%')
        # plt.title(f"Monthly Returns Heatmap - {strategy_name}")
        # plt.xlabel('Month')
        # plt.ylabel('Year')
        # # Adjust month labels
        # plt.xticks(range(1, 13), ['Jan', 'Feb', 'Mar', 'Apr',
        #            'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])
        # plt.savefig(f"{strategy_name}_monthly_returns_heatmap.png")
        # plt.close()

        # # Plotting the price, EMAs, and buy/sell signals
        # fig = go.Figure()

        # # Add close price trace
        # fig.add_trace(go.Scatter(x=strategy_df['timestamp'], y=strategy_df['close_price'],
        #               mode='lines', name='Close Price', line=dict(color='blue')))

        # # Add EMA traces
        # fig.add_trace(go.Scatter(
        #     x=strategy_df['timestamp'], y=strategy_df['EMA_short'], mode='lines', name='EMA 5', line=dict(color='red')))
        # fig.add_trace(go.Scatter(
        #     x=strategy_df['timestamp'], y=strategy_df['EMA_long'], mode='lines', name='EMA 13', line=dict(color='green')))

        # # Add buy signals from df_basic
        # buy_signals = df_basic[['entry_timestamp', 'entry_price']].rename(
        #     columns={'entry_timestamp': 'timestamp', 'entry_price': 'price'}
        # )
        # fig.add_trace(go.Scatter(x=buy_signals['timestamp'], y=buy_signals['price'], mode='markers',
        #             marker_symbol='triangle-up', marker_color='green', marker_size=12, name='Buy Signal'))

        # # Add sell signals from df_basic
        # sell_signals = df_basic[['exit_timestamp', 'exit_price']].rename(
        #     columns={'exit_timestamp': 'timestamp', 'exit_price': 'price'}
        # )
        # fig.add_trace(go.Scatter(x=sell_signals['timestamp'], y=sell_signals['price'], mode='markers',
        #             marker_symbol='triangle-down', marker_color='red', marker_size=12, name='Sell Signal'))

        # # Update layout
        # fig.update_layout(
        #     title='Price and EMA with Buy/Sell Signals',
        #     xaxis_title='Date',
        #     yaxis_title='Price',
        #     legend_title='Legend',
        #     template='plotly_white'
        # )

        # Show the figure
        #fig.show()

        # Save the figure as a PNG file
        #fig.write_image(f"{strategy_name}_buy_sell.png")


    def monte_carlo_simulation(self, df: pd.DataFrame, iterations: int = 1000) -> dict:
        # Extract profit/loss values as monetary amounts
        returns = df['profit_loss'].values

        simulated_final_balances = []

        for _ in range(iterations):
            # Resample monetary returns with replacement
            # Convert to numpy array first to avoid type issues
            returns_array = np.array(returns)
            sim_returns = np.random.choice(returns_array, size=len(returns_array), replace=True)
            # Compute cumulative sum for this simulation
            cumulative_balance = np.sum(sim_returns)
            simulated_final_balances.append(cumulative_balance)

        return {
            'var_95': np.percentile(simulated_final_balances, 5),        # 5th percentile (Value at Risk 95%)
            'var_99': np.percentile(simulated_final_balances, 1),        # 1st percentile (Value at Risk 99%)
            'expected_return': np.mean(simulated_final_balances),        # Average final balance
            'return_std': np.std(simulated_final_balances),              # Standard deviation of final balances
            'worst_case': np.min(simulated_final_balances),              # Worst simulated final balance
            'best_case': np.max(simulated_final_balances)                # Best simulated final balance
        }

    def analyze_time_patterns(self, aggregated_trades: pd.DataFrame) -> dict:
        # Ensure entry timestamps are in datetime format
        aggregated_trades['entry_timestamp'] = pd.to_datetime(aggregated_trades['entry_timestamp'])

        # Extract hour and day of the week
        aggregated_trades['hour'] = aggregated_trades['entry_timestamp'].dt.hour
        aggregated_trades['day_of_week'] = aggregated_trades['entry_timestamp'].dt.day_name()

        return {
            'best_hour': aggregated_trades.groupby('hour')['profit_loss'].mean().idxmax(),
            'worst_hour': aggregated_trades.groupby('hour')['profit_loss'].mean().idxmin(),
            'best_day': aggregated_trades.groupby('day_of_week')['profit_loss'].mean().idxmax(),
            'worst_day': aggregated_trades.groupby('day_of_week')['profit_loss'].mean().idxmin(),
            'hourly_stats': aggregated_trades.groupby('hour')['profit_loss'].agg(['mean', 'count', 'sum']).to_dict(),
            'daily_stats': aggregated_trades.groupby('day_of_week')['profit_loss'].agg(['mean', 'count', 'sum']).to_dict()
        }


    def log_backtest_metrics(self, metrics):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s: %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

        logging.info("Backtest Time Analysis:")
        logging.info(f"  Best Hour: {metrics['time_analysis']['best_hour']}")
        logging.info(f"  Worst Hour: {metrics['time_analysis']['worst_hour']}")
        logging.info(f"  Best Day: {metrics['time_analysis']['best_day']}")
        logging.info(f"  Worst Day: {metrics['time_analysis']['worst_day']}")

        logging.info("  Hourly Stats:")
        hourly_stats = metrics['time_analysis']['hourly_stats']
        for hour, stats in hourly_stats['mean'].items():
            logging.info(f"    Hour {hour:02d}: Mean={stats:.2f}, Count={hourly_stats['count'][hour]}, Sum={hourly_stats['sum'][hour]:.2f}")

        logging.info("  Daily Stats:")
        daily_stats = metrics['time_analysis']['daily_stats']
        for day, stats in daily_stats['mean'].items():
            logging.info(f"    {day}: Mean={stats:.2f}, Count={daily_stats['count'][day]}, Sum={daily_stats['sum'][day]:.2f}")


        logging.info("Backtest Risk Analysis:")
        for metric, value in metrics['risk_analysis'].items():
            logging.info(f"  {metric.capitalize()}: {value}")
