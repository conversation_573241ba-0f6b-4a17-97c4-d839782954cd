"""
Training Orchestrator for Comprehensive PPO Trading Model Training

This module provides a unified interface for all training operations including
initial training, hyperparameter optimization, walk-forward validation,
and incremental learning for live trading.
"""

import os
import sys
import logging
import argparse
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Optional, Any
from pathlib import Path

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

from app.training.comprehensive_training_system import (
    ComprehensiveTrainingSystem, TrainingConfig
)
from app.training.hyperparameter_optimizer import HyperparameterOptimizer
from app.training.walk_forward_validation import WalkForwardValidator, WalkForwardConfig
from app.training.incremental_learning import IncrementalLearningSystem, IncrementalConfig
from app.db.db_executor import DatabaseExecutor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TrainingOrchestrator:
    """Main orchestrator for all training operations"""

    def __init__(self, db: DatabaseExecutor, output_dir: str = "./training_output"):
        self.db = db
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Initialize subsystems
        self.training_system = ComprehensiveTrainingSystem(db, str(self.output_dir / "basic_training"))
        self.optimizer = HyperparameterOptimizer(db, str(self.output_dir / "optimization"))
        self.validator = WalkForwardValidator(db, str(self.output_dir / "walk_forward"))
        self.incremental_system = IncrementalLearningSystem(db, str(self.output_dir / "incremental"))

    def get_default_config(self, pair: str = "SOL/USD") -> TrainingConfig:
        """Get default training configuration with recommended parameters"""
        return TrainingConfig(
            pair=pair,
            start_date=datetime(2021, 6, 17, tzinfo=timezone.utc),
            end_date=datetime(2025, 7, 10, tzinfo=timezone.utc),

            # Optimized hyperparameters based on analysis
            learning_rate_initial=0.0003,
            learning_rate_final=0.00005,
            n_steps=1024,
            batch_size=128,
            n_epochs=10,
            gamma=0.97,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef_initial=0.05,
            ent_coef_final=0.01,
            vf_coef=0.5,
            max_grad_norm=0.5,

            total_timesteps=100000,
            random_seeds=[42, 123, 456, 789, 999],
            evaluation_frequency=10000,
            save_frequency=25000
        )

    async def run_initial_training(self, config: Optional[TrainingConfig] = None) -> str:
        """Run initial model training with multiple seeds"""
        logger.info("Starting initial training phase")

        if config is None:
            config = self.get_default_config()

        # Run experiment
        results = await self.training_system.run_experiment(config)

        if not results:
            raise RuntimeError("Initial training failed - no results obtained")

        # Find best model
        best_result = max(results, key=lambda r: r.metrics.sharpe_ratio)
        best_model_path = best_result.model_path

        logger.info(f"Initial training completed. Best model: {best_model_path}")
        logger.info(f"Best Sharpe ratio: {best_result.metrics.sharpe_ratio:.4f}")

        return best_model_path

    async def run_hyperparameter_optimization(self, base_config: Optional[TrainingConfig] = None,
                                            n_trials: int = 50) -> TrainingConfig:
        """Run hyperparameter optimization"""
        logger.info(f"Starting hyperparameter optimization with {n_trials} trials")

        if base_config is None:
            base_config = self.get_default_config()

        # Run optimization
        study = self.optimizer.optimize(base_config, n_trials=n_trials)

        # Get best configuration
        best_config = self.optimizer.get_best_config(study, base_config)

        logger.info("Hyperparameter optimization completed")
        logger.info(f"Best Sharpe ratio: {study.best_value:.4f}")

        return best_config

    async def run_walk_forward_validation(self, config: Optional[TrainingConfig] = None) -> dict[str, Any]:
        """Run walk-forward validation"""
        logger.info("Starting walk-forward validation")

        if config is None:
            config = self.get_default_config()

        # Create walk-forward configuration
        wf_config = WalkForwardConfig(
            pair=config.pair,
            start_date=config.start_date,
            end_date=config.end_date,
            training_months=12,
            testing_months=3,
            step_months=1
        )

        # Run validation
        results = await self.validator.run_walk_forward_validation(wf_config, config)

        # Print summary
        self.validator.print_walk_forward_summary(results)

        # Return analysis
        analysis = self.validator.analyze_walk_forward_results(results)

        logger.info("Walk-forward validation completed")
        return analysis

    async def setup_incremental_learning(self, model_path: str, pair: str = "SOL/USD") -> IncrementalConfig:
        """Setup incremental learning for live trading"""
        logger.info(f"Setting up incremental learning for model: {model_path}")

        config = IncrementalConfig(
            pair=pair,
            model_path=model_path,
            update_frequency_days=7,
            training_window_months=18,
            validation_window_months=3,
            min_new_samples=500,
            learning_rate_decay=0.8,
            performance_threshold=0.1
        )

        logger.info("Incremental learning configuration created")
        return config

    async def run_incremental_update(self, config: IncrementalConfig):
        """Run incremental model update"""
        logger.info("Running incremental model update")

        result = await self.incremental_system.run_incremental_learning_cycle(config)

        if result:
            logger.info(f"Incremental update completed")
            logger.info(f"Model updated: {result.model_updated}")
            logger.info(f"Performance improvement: {result.improvement:.4f}")
        else:
            logger.info("No incremental update needed")

        return result

    async def run_complete_training_pipeline(self, pair: str = "SOL/USD",
                                           optimize_hyperparams: bool = True,
                                           run_validation: bool = True) -> dict[str, Any]:
        """Run the complete training pipeline"""
        logger.info("="*60)
        logger.info("STARTING COMPLETE TRAINING PIPELINE")
        logger.info("="*60)

        results = {}

        try:
            # Step 1: Hyperparameter optimization (optional)
            if optimize_hyperparams:
                logger.info("\n" + "="*40)
                logger.info("STEP 1: HYPERPARAMETER OPTIMIZATION")
                logger.info("="*40)

                best_config = await self.run_hyperparameter_optimization(n_trials=20)
                results['optimization'] = {
                    'completed': True,
                    'best_config': best_config.__dict__
                }
            else:
                best_config = self.get_default_config(pair)
                results['optimization'] = {'completed': False}

            # Step 2: Initial training with best configuration
            logger.info("\n" + "="*40)
            logger.info("STEP 2: INITIAL TRAINING")
            logger.info("="*40)

            best_model_path = await self.run_initial_training(best_config)
            results['initial_training'] = {
                'completed': True,
                'best_model_path': best_model_path
            }

            # Step 3: Walk-forward validation (optional)
            if run_validation:
                logger.info("\n" + "="*40)
                logger.info("STEP 3: WALK-FORWARD VALIDATION")
                logger.info("="*40)

                validation_analysis = await self.run_walk_forward_validation(best_config)
                results['validation'] = {
                    'completed': True,
                    'analysis': validation_analysis
                }
            else:
                results['validation'] = {'completed': False}

            # Step 4: Setup incremental learning
            logger.info("\n" + "="*40)
            logger.info("STEP 4: INCREMENTAL LEARNING SETUP")
            logger.info("="*40)

            incremental_config = await self.setup_incremental_learning(best_model_path, pair)
            results['incremental_setup'] = {
                'completed': True,
                'config': incremental_config.__dict__
            }

            logger.info("\n" + "="*60)
            logger.info("TRAINING PIPELINE COMPLETED SUCCESSFULLY")
            logger.info("="*60)

            # Summary
            logger.info("\nPIPELINE SUMMARY:")
            logger.info(f"  Best model: {best_model_path}")
            if optimize_hyperparams:
                logger.info(f"  Hyperparameter optimization: ✓")
            if run_validation:
                consistency = validation_analysis['time_analysis']['consistency_ratio']
                logger.info(f"  Walk-forward validation: ✓ (Consistency: {consistency:.2%})")
            logger.info(f"  Incremental learning: ✓ Ready for live trading")

            results['pipeline_status'] = 'completed'
            results['summary'] = {
                'best_model_path': best_model_path,
                'ready_for_live_trading': True
            }

        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            results['pipeline_status'] = 'failed'
            results['error'] = str(e)
            raise

        return results


async def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description='PPO Trading Model Training Orchestrator')
    parser.add_argument('--mode', choices=['initial', 'optimize', 'validate', 'incremental', 'pipeline'],
                       default='pipeline', help='Training mode to run')
    parser.add_argument('--pair', default='SOL/USD', help='Trading pair')
    parser.add_argument('--model-path', help='Path to existing model (for incremental learning)')
    parser.add_argument('--trials', type=int, default=20, help='Number of optimization trials')
    parser.add_argument('--no-optimize', action='store_true', help='Skip hyperparameter optimization')
    parser.add_argument('--no-validate', action='store_true', help='Skip walk-forward validation')

    args = parser.parse_args()

    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME'),
        db_user=os.getenv('DB_USER'),
        db_password=os.getenv('DB_PASSWORD'),
        db_host=os.getenv('DB_HOST'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )

    # Create orchestrator
    orchestrator = TrainingOrchestrator(db)

    try:
        if args.mode == 'initial':
            await orchestrator.run_initial_training()

        elif args.mode == 'optimize':
            await orchestrator.run_hyperparameter_optimization(n_trials=args.trials)

        elif args.mode == 'validate':
            await orchestrator.run_walk_forward_validation()

        elif args.mode == 'incremental':
            if not args.model_path:
                raise ValueError("Model path required for incremental learning")
            config = await orchestrator.setup_incremental_learning(args.model_path, args.pair)
            await orchestrator.run_incremental_update(config)

        elif args.mode == 'pipeline':
            await orchestrator.run_complete_training_pipeline(
                pair=args.pair,
                optimize_hyperparams=not args.no_optimize,
                run_validation=not args.no_validate
            )

    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(main())
