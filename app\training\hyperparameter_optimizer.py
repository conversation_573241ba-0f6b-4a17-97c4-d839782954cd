"""
Hyperparameter Optimization for PPO Trading Models

This module implements systematic hyperparameter optimization using Optuna
with proper cross-validation and statistical significance testing.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any
import optuna
from optuna.pruners import <PERSON>n<PERSON>run<PERSON>
from optuna.samplers import TPESampler

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

from app.training.comprehensive_training_system import (
    ComprehensiveTrainingSystem, TrainingConfig, EvaluationMetrics
)
from app.db.db_executor import DatabaseExecutor

logger = logging.getLogger(__name__)


class HyperparameterOptimizer:
    """Systematic hyperparameter optimization for PPO trading models"""
    
    def __init__(self, db: DatabaseExecutor, output_dir: str = "./optimization_results"):
        self.db = db
        self.training_system = ComprehensiveTrainingSystem(db, output_dir)
        self.output_dir = output_dir
        
        # Define hyperparameter search spaces
        self.param_ranges = {
            # Learning rate
            'learning_rate_initial': (0.0001, 0.001),
            'learning_rate_final': (0.00001, 0.0001),
            
            # PPO specific parameters
            'n_steps': [512, 1024, 2048],
            'batch_size': [64, 128, 256],
            'n_epochs': [5, 10, 15, 20],
            'gamma': (0.95, 0.999),
            'gae_lambda': (0.9, 0.99),
            'clip_range': (0.1, 0.3),
            'ent_coef_initial': (0.01, 0.1),
            'vf_coef': (0.25, 1.0),
            'max_grad_norm': (0.3, 1.0),
            
            # Training parameters
            'total_timesteps': [50000, 100000, 200000]
        }
    
    def create_config_from_trial(self, trial: optuna.Trial, base_config: TrainingConfig) -> TrainingConfig:
        """Create training configuration from Optuna trial"""
        
        # Sample hyperparameters
        learning_rate_initial = trial.suggest_float(
            'learning_rate_initial', *self.param_ranges['learning_rate_initial'], log=True
        )
        learning_rate_final = trial.suggest_float(
            'learning_rate_final', *self.param_ranges['learning_rate_final'], log=True
        )
        
        n_steps = trial.suggest_categorical('n_steps', self.param_ranges['n_steps'])
        batch_size = trial.suggest_categorical('batch_size', self.param_ranges['batch_size'])
        
        # Ensure batch_size divides n_steps
        while n_steps % batch_size != 0:
            batch_size = trial.suggest_categorical('batch_size', self.param_ranges['batch_size'])
        
        n_epochs = trial.suggest_categorical('n_epochs', self.param_ranges['n_epochs'])
        gamma = trial.suggest_float('gamma', *self.param_ranges['gamma'])
        gae_lambda = trial.suggest_float('gae_lambda', *self.param_ranges['gae_lambda'])
        clip_range = trial.suggest_float('clip_range', *self.param_ranges['clip_range'])
        ent_coef_initial = trial.suggest_float('ent_coef_initial', *self.param_ranges['ent_coef_initial'])
        vf_coef = trial.suggest_float('vf_coef', *self.param_ranges['vf_coef'])
        max_grad_norm = trial.suggest_float('max_grad_norm', *self.param_ranges['max_grad_norm'])
        total_timesteps = trial.suggest_categorical('total_timesteps', self.param_ranges['total_timesteps'])
        
        # Create new config with optimized parameters
        config = TrainingConfig(
            pair=base_config.pair,
            start_date=base_config.start_date,
            end_date=base_config.end_date,
            train_ratio=base_config.train_ratio,
            validation_ratio=base_config.validation_ratio,
            test_ratio=base_config.test_ratio,
            
            # Optimized hyperparameters
            learning_rate_initial=learning_rate_initial,
            learning_rate_final=learning_rate_final,
            n_steps=n_steps,
            batch_size=batch_size,
            n_epochs=n_epochs,
            gamma=gamma,
            gae_lambda=gae_lambda,
            clip_range=clip_range,
            ent_coef_initial=ent_coef_initial,
            ent_coef_final=ent_coef_initial * 0.2,  # Decay to 20% of initial
            vf_coef=vf_coef,
            max_grad_norm=max_grad_norm,
            total_timesteps=total_timesteps,
            
            # Use fewer seeds for optimization to speed up
            random_seeds=[42, 123, 456],
            evaluation_frequency=base_config.evaluation_frequency,
            save_frequency=base_config.save_frequency
        )
        
        return config
    
    def objective(self, trial: optuna.Trial, base_config: TrainingConfig) -> float:
        """Objective function for Optuna optimization"""
        try:
            # Create configuration from trial
            config = self.create_config_from_trial(trial, base_config)
            
            # Run experiment with multiple seeds
            import asyncio
            results = asyncio.run(self.training_system.run_experiment(config))
            
            if not results:
                logger.warning(f"Trial {trial.number}: No results obtained")
                return -np.inf
            
            # Calculate mean Sharpe ratio across seeds
            sharpe_ratios = [result.metrics.sharpe_ratio for result in results]
            mean_sharpe = np.mean(sharpe_ratios)
            std_sharpe = np.std(sharpe_ratios)
            
            # Penalize high variance (prefer consistent performance)
            consistency_penalty = std_sharpe * 0.1
            objective_value = mean_sharpe - consistency_penalty
            
            # Log intermediate results
            logger.info(f"Trial {trial.number}: Sharpe {mean_sharpe:.4f} ± {std_sharpe:.4f}, "
                       f"Objective: {objective_value:.4f}")
            
            # Report intermediate value for pruning
            trial.report(objective_value, step=0)
            
            # Check if trial should be pruned
            if trial.should_prune():
                raise optuna.TrialPruned()
            
            return objective_value
            
        except Exception as e:
            logger.error(f"Trial {trial.number} failed: {e}")
            return -np.inf
    
    def optimize(self, base_config: TrainingConfig, n_trials: int = 50, 
                timeout: Optional[int] = None) -> optuna.Study:
        """Run hyperparameter optimization"""
        
        logger.info(f"Starting hyperparameter optimization with {n_trials} trials")
        
        # Create study
        study = optuna.create_study(
            direction='maximize',
            sampler=TPESampler(seed=42),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        
        # Run optimization
        study.optimize(
            lambda trial: self.objective(trial, base_config),
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=True
        )
        
        # Log results
        logger.info("Optimization completed!")
        logger.info(f"Best trial: {study.best_trial.number}")
        logger.info(f"Best value: {study.best_value:.4f}")
        logger.info("Best parameters:")
        for key, value in study.best_params.items():
            logger.info(f"  {key}: {value}")
        
        # Save study
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        study_file = f"{self.output_dir}/optimization_study_{timestamp}.pkl"
        optuna.storages.save_study(study, study_file)
        logger.info(f"Study saved to {study_file}")
        
        return study
    
    def get_best_config(self, study: optuna.Study, base_config: TrainingConfig) -> TrainingConfig:
        """Get the best configuration from optimization study"""
        best_trial = study.best_trial
        return self.create_config_from_trial(best_trial, base_config)
    
    def analyze_optimization_results(self, study: optuna.Study) -> Dict[str, Any]:
        """Analyze optimization results and provide insights"""
        
        # Get completed trials
        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        
        if not completed_trials:
            return {"error": "No completed trials found"}
        
        # Extract parameters and values
        params_df = pd.DataFrame([t.params for t in completed_trials])
        values = [t.value for t in completed_trials]
        params_df['objective_value'] = values
        
        # Parameter importance
        importance = optuna.importance.get_param_importances(study)
        
        # Top performing trials
        top_trials = sorted(completed_trials, key=lambda t: t.value, reverse=True)[:5]
        
        analysis = {
            'n_trials': len(completed_trials),
            'best_value': study.best_value,
            'best_params': study.best_params,
            'parameter_importance': importance,
            'top_5_trials': [
                {
                    'trial_number': t.number,
                    'value': t.value,
                    'params': t.params
                } for t in top_trials
            ],
            'parameter_statistics': {
                param: {
                    'mean': params_df[param].mean() if param in params_df else None,
                    'std': params_df[param].std() if param in params_df else None,
                    'min': params_df[param].min() if param in params_df else None,
                    'max': params_df[param].max() if param in params_df else None
                } for param in importance.keys()
            }
        }
        
        return analysis


def main():
    """Example usage of hyperparameter optimization"""
    
    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME'),
        db_user=os.getenv('DB_USER'),
        db_password=os.getenv('DB_PASSWORD'),
        db_host=os.getenv('DB_HOST'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    # Create base configuration
    base_config = TrainingConfig(
        pair="SOL/USD",
        start_date=datetime(2021, 6, 17, tzinfo=timezone.utc),
        end_date=datetime(2025, 7, 10, tzinfo=timezone.utc),
        total_timesteps=50000,  # Reduced for optimization speed
        random_seeds=[42, 123]  # Fewer seeds for optimization
    )
    
    # Run optimization
    optimizer = HyperparameterOptimizer(db)
    study = optimizer.optimize(base_config, n_trials=20)
    
    # Analyze results
    analysis = optimizer.analyze_optimization_results(study)
    print("\nOptimization Analysis:")
    print(f"Best Sharpe ratio: {analysis['best_value']:.4f}")
    print("\nParameter Importance:")
    for param, importance in analysis['parameter_importance'].items():
        print(f"  {param}: {importance:.4f}")
    
    # Get best configuration
    best_config = optimizer.get_best_config(study, base_config)
    print(f"\nBest configuration saved for final training")
    
    db.close()


if __name__ == "__main__":
    main()
