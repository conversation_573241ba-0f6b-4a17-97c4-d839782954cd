import logging
import os
import sys
import traceback
import optuna
from dataclasses import dataclass
from datetime import datetime
from multiprocessing import cpu_count
from os import getenv
from typing import Dict, List, Optional, Any
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

import numpy as np
import pandas as pd
from scipy.optimize import minimize
from scipy.stats import qmc
from app.backtester.backtestAnalyzer import BacktestAnalyzer
from app.db.db_executor import DatabaseExecutor
from app.backtester.backtester import Backtester
from app.strategy.ema_strategy import EMAStrategy
from app.trade_manager.trade_manager import TradeManager
from app.trade_manager.portfolio_manager import PortfolioManager
from app.scripts.simulated_order_executor import SimulatedOrderExecutor
from app.trade_history.trade_history import TradeHistory

# Setup logging
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """Data class to store optimization results"""
    parameters: dict[str, Any]  # Can contain int or float values
    profit: float
    success: bool
    message: str

def run_optimization(point, ohlc_data, pair, param_names, bounds, interval, initial_portfolio, taker_maker_fee, min_investment):
    """Run optimization from a single starting point with all parameters passed explicitly."""
    try:
        def local_objective_function(params):
            try:
                param_dict = {name: value for name, value in zip(param_names, params)}

                take_profit_levels = [
                    param_dict['take_profit_level1'],
                    param_dict['take_profit_level2'],
                    param_dict['take_profit_level3']
                ]

                strategy = EMAStrategy(
                    pair=pair,
                    short_window=int(param_dict['short_window']),
                    long_window=int(param_dict['long_window']),
                    take_profit_levels=take_profit_levels,
                    stop_loss=param_dict['stop_loss'],
                    interval=interval,
                    investment_percentage=param_dict['investment_percentage'],
                    max_investment=param_dict['max_investment'],
                    taker_maker_fee=taker_maker_fee,
                    min_investment=min_investment,
                    min_body_ratio=param_dict['min_body_ratio'],
                    max_upper_wick_ratio=param_dict['max_upper_wick_ratio'],
                    min_volume_multiplier=param_dict['min_volume_multiplier'],
                    pullback_threshold=param_dict['pullback_threshold']
                )

                preprocessed_df = strategy.preprocess_data(ohlc_data.copy())

                # Simplified for optimization - using default initialization
                currency = "USD"  # Assuming USD as the currency, should be passed as a parameter if different
                mode = "backtest"  # Always backtest mode for optimization

                portfolio_manager =PortfolioManager(
                    initial_portfolio, None, 'SOL', currency,
                )

                # Create trade history with backtest mode
                trade_history = TradeHistory(mode=mode, db=None, logger=logger)

                # Initialize trade manager properly
                order_executor = SimulatedOrderExecutor()
                trade_manager = TradeManager(
                    portfolio_manager=portfolio_manager,
                    order_executor=order_executor,
                    trade_history=trade_history,
                    api=None,
                    mode=mode
                )

                backtester = Backtester(
                    None,  # No DB dependency
                    strategy,
                    pair,
                    None,  # No start_date needed when data is provided
                    None,  # No end_date needed when data is provided
                    trade_manager,
                    portfolio_manager
                )

                _, results = backtester.run_backtest(preprocessed_df)  # Ignore trades, only need results

                if not results:
                    return 0.0

                df = pd.DataFrame(results)
                final_profit = df['profit_loss'].sum()
                return -final_profit

            except Exception as e:
                logging.error(f"Error in optimization iteration: {str(e)}")
                return 0.0

        result = minimize(
            local_objective_function,
            point,
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': 50, 'disp': False}
        )

        parameters = {
            name: (int(value) if name in ['short_window', 'long_window'] else value)
            for name, value in zip(param_names, result.x)
        }

        return OptimizationResult(
            parameters=parameters,
            profit=-result.fun,
            success=result.success,
            message=result.message
        )

    except Exception as e:
        logging.error(f"Single-start optimization failed: {str(e)}")
        return OptimizationResult(
            parameters={},
            profit=0.0,
            success=False,
            message=str(e)
        )

class StrategyOptimizer:
    def __init__(
        self,
        db,
        pair: str,
        initial_portfolio: float,
        taker_maker_fee: float,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1h',
        n_workers: Optional[int] = None,
        min_investment: float = 200,
    ):
        self.db = db
        self.pair = pair
        self.initial_portfolio = initial_portfolio
        self.taker_maker_fee = taker_maker_fee
        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.n_workers = n_workers or max(1, cpu_count() - 1)
        self.min_investment = min_investment

        # Define parameter bounds and names based on EMAStrategy parameters
        self.param_bounds = {
            'investment_percentage': (0.4, 1.0),
            'short_window': (5, 13),
            'long_window': (13, 25),
            'stop_loss': (-0.3, -0.01),
            'take_profit_level1': (0.05, 0.3),
            'take_profit_level2': (0.2, 0.5),
            'take_profit_level3': (0.5, 0.8),
            'max_investment': (2500, 10000),
            'min_body_ratio': (0.5, 0.8),
            'max_upper_wick_ratio': (0.1, 0.3),
            'min_volume_multiplier': (1.5, 2.5),
            'pullback_threshold': (0.97, 0.995)
        }

        self.bounds = list(self.param_bounds.values())
        self.param_names = list(self.param_bounds.keys())
        self.ohlc_data = self.get_ohlc_data()

    def get_ohlc_data(self) -> pd.DataFrame:
        """Fetch raw OHLC data from the database"""
        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume
        FROM kraken_ohlc
        WHERE pair = %s AND timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        results = self.db.execute_select(query, (self.pair, self.start_date, self.end_date),
                                         float_columns=[1, 2, 3, 4, 5])
        df = pd.DataFrame(results, columns=['timestamp', 'open_price', 'high_price',
                                            'low_price', 'close_price', 'volume'])
        return df

    def generate_initial_points(self, n_points: int = 10) -> list[np.ndarray]:
        """Generate multiple starting points using Latin Hypercube Sampling"""
        sampler = qmc.LatinHypercube(d=len(self.bounds))
        sample = sampler.random(n=n_points)

        points = []
        for point in sample:
            scaled_point = []
            for i, (low, high) in enumerate(self.bounds):
                if self.param_names[i] in ['short_window', 'long_window']:
                    scaled_value = int(low + point[i] * (high - low))
                else:
                    scaled_value = low + point[i] * (high - low)
                scaled_point.append(scaled_value)
            points.append(np.array(scaled_point))

        return points

    def optimize(self, n_trials: int = 50) -> dict:
        """Run hyperparameter optimization using Optuna"""
        ohlc_data = self.ohlc_data.copy()

        def objective(trial: optuna.Trial):
            try:
                param_dict = {
                    'investment_percentage': trial.suggest_float('investment_percentage', 0.4, 1.0),
                    'short_window': trial.suggest_int('short_window', 5, 21),
                    'long_window': trial.suggest_int('long_window', 13, 25),
                    'stop_loss': trial.suggest_float('stop_loss', -0.3, -0.01),
                    'take_profit_level1': trial.suggest_float('take_profit_level1', 0.05, 0.5),
                    'take_profit_level2': trial.suggest_float('take_profit_level2', 0.2, 0.7),
                    'take_profit_level3': trial.suggest_float('take_profit_level3', 0.5, 0.9),
                    'max_investment': trial.suggest_float('max_investment', 2500, 10000),
                    'min_body_ratio': trial.suggest_float('min_body_ratio', 0.5, 0.7),
                    'max_upper_wick_ratio': trial.suggest_float('max_upper_wick_ratio', 0.2, 0.3),
                    'min_volume_multiplier': trial.suggest_float('min_volume_multiplier', 1.5, 2.5),
                    'pullback_threshold': trial.suggest_float('pullback_threshold', 0.97, 0.995),
                }

                take_profit_levels = [
                    param_dict['take_profit_level1'],
                    param_dict['take_profit_level2'],
                    param_dict['take_profit_level3']
                ]

                strategy = EMAStrategy(
                    pair=self.pair,
                    short_window=param_dict['short_window'],
                    long_window=param_dict['long_window'],
                    take_profit_levels=take_profit_levels,
                    stop_loss=param_dict['stop_loss'],
                    interval=self.interval,
                    investment_percentage=param_dict['investment_percentage'],
                    max_investment=param_dict['max_investment'],
                    taker_maker_fee=self.taker_maker_fee,
                    min_investment=self.min_investment,
                    min_body_ratio=param_dict['min_body_ratio'],
                    max_upper_wick_ratio=param_dict['max_upper_wick_ratio'],
                    min_volume_multiplier=param_dict['min_volume_multiplier'],
                    pullback_threshold=param_dict['pullback_threshold']
                )

                preprocessed_df = strategy.preprocess_data(ohlc_data.copy())

                portfolio_manager = PortfolioManager(
                    self.initial_portfolio, None, self.pair[0:3], self.pair[3:3],
                )

                trade_history = TradeHistory(mode="backtest", db=None, logger=logger)
                order_executor = SimulatedOrderExecutor()
                trade_manager = TradeManager(portfolio_manager, order_executor, trade_history, api=None, mode="backtest")

                backtester = Backtester(
                    db=None,
                    strategy=strategy,
                    pair=self.pair,
                    start_date=None,
                    end_date=None,
                    trade_manager=trade_manager,
                    portfolio_manager=portfolio_manager
                )

                _, results = backtester.run_backtest(preprocessed_df)  # Ignore trades, only need results
                if not results:
                    raise optuna.TrialPruned()

                df = pd.DataFrame(results)
                final_profit = df['profit_loss'].sum()

                return final_profit

            except Exception as e:
                logger.error(f"Trial failed: {e}")
                raise optuna.TrialPruned()

        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials, n_jobs=self.n_workers)

        best = study.best_trial

        optimal_params = {
            'parameters': {
                **best.params,
                'take_profit_levels': [
                    best.params['take_profit_level1'],
                    best.params['take_profit_level2'],
                    best.params['take_profit_level3']
                ]
            },
            'expected_profit': best.value,
            'success': True,
            'message': f"Best trial completed successfully with profit: {best.value}",
            'n_successful_optimizations': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        }

        return optimal_params

if __name__ == "__main__" or os.getenv("RUN_MAIN"):
    try:
        import dotenv
        dotenv.load_dotenv()
        env = getenv("ENVIRONMENT")
        mode = "backtest"  # Always run in backtest mode for optimization

        db = DatabaseExecutor(
            db_name=getenv('DB_NAME'),
            db_user=getenv('DB_USER'),
            db_password=getenv('DB_PASSWORD'),
            db_host=getenv('DB_HOST'),
            db_port=getenv('DB_PORT')
        )

        params = {
            'pair': 'SOLUSD',
            'initial_portfolio': 5000,
            'taker_maker_fee': 0.0025,
            'min_investment': 200,
            'interval': '1h'
        }

        start_date = datetime(2024, 1, 1)
        end_date = datetime(2025, 2, 4)
        opt_number = 50

        optimizer = StrategyOptimizer(
            db=db,
            pair=params['pair'],
            initial_portfolio=params['initial_portfolio'],
            taker_maker_fee=params['taker_maker_fee'],
            start_date=start_date,
            end_date=end_date,
            interval=params['interval'],
            n_workers=8,
            min_investment=params['min_investment']
        )

        logger.info("Starting optimization process...")
        optimal_params = optimizer.optimize(n_trials=opt_number)

        if not optimal_params or not optimal_params.get('success'):
            logger.error("Optimization failed to find optimal parameters")
            sys.exit(1)

        print("\nOptimization Results:")
        print("-" * 50)
        print("Optimal Parameters:")
        for param_name, param_value in optimal_params['parameters'].items():
            if isinstance(param_value, list):
                print(f"{param_name}:")
                for i, v in enumerate(param_value, 1):
                    print(f"  Level {i}: {v:.4f}")
            else:
                print(f"{param_name}: {param_value:.4f}")

        print(f"\nExpected Profit: ${optimal_params['expected_profit']:.2f}")
        print(f"Successful Optimizations: {optimal_params['n_successful_optimizations']}")
        print("-" * 50)

        try:
            strategy = EMAStrategy(
                pair=params['pair'],
                short_window=int(optimal_params['parameters']['short_window']),
                long_window=int(optimal_params['parameters']['long_window']),
                take_profit_levels=optimal_params['parameters']['take_profit_levels'],
                stop_loss=optimal_params['parameters']['stop_loss'],
                interval=params['interval'],
                investment_percentage=optimal_params['parameters']['investment_percentage'],
                max_investment=optimal_params['parameters']['max_investment'],
                taker_maker_fee=params['taker_maker_fee'],
                min_investment=params['min_investment'],
                min_body_ratio=optimal_params['parameters']['min_body_ratio'],
                max_upper_wick_ratio=optimal_params['parameters']['max_upper_wick_ratio'],
                min_volume_multiplier=optimal_params['parameters']['min_volume_multiplier'],
                pullback_threshold=optimal_params['parameters']['pullback_threshold']
            )

            # Initialize components properly like in second file
            portfolio_manager = PortfolioManager(params['initial_portfolio'], None, params['pair'][0:3], params['pair'][3:3])
            trade_history = TradeHistory(mode=mode, db=db, logger=logger)
            order_executor = SimulatedOrderExecutor()
            trade_manager = TradeManager(
                portfolio_manager=portfolio_manager,
                order_executor=order_executor,
                trade_history=trade_history,
                api=None,
                mode=mode
            )

            backtester = Backtester(
                db,
                strategy,
                params['pair'],
                start_date,
                end_date,
                trade_manager,
                portfolio_manager
            )

            preprocessed_df = strategy.preprocess_data(optimizer.ohlc_data)
            trades, results = backtester.run_backtest(preprocessed_df)

            backtester.clear_previous_results()
            backtester.save_results(trades, results)

            analyzer = BacktestAnalyzer()
            # Adjust parameters to match the expected signature (4 parameters)
            analyzer.analyze_results(db, strategy.__class__.__name__, params['initial_portfolio'], params['pair'])

            logger.info("Final backtest completed successfully")

        except Exception as e:
            logger.error(f"Final backtest failed: {str(e)}")
            logger.error(traceback.format_exc())
            optimal_params['final_backtest_error'] = str(e)

        logger.info(f"Optimization completed: {optimal_params}")

    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        logger.error(traceback.format_exc())
        raise
