# Pytrader
Crypto trading bot for Kraken

## SETUP instructions
1. Create postgres docker container from docker-compose.yaml: 
    RUN docker-compose up db
2. Create database: python -m app.init.setup_database
3. Import ohlc kraken data: python app/init/import_kraken_ohlc.py
    REMEMBER - the file ohlc_data should be named SOLUSD_1.csv --> a minute data
4. Import ohlc binance data: python app/init/import_binance_ohlc.py
5. Backtest strategy: python app/backtester/backtester.py
6. Optimizing backtest python app/backtester/optimizer.py
7. Run the Dashboard: python app/dashboard/dash.py
   Access: Open your browser at http://localhost:8050.
8. Transfer to server: scp -P 5461 docker-compose.yaml dule@**************:/home/<USER>/pyTrader
9. Transfer all to server: scp -P 5461 -r . dule@**************:/home/<USER>/pyTrader 

### PPO Strategy
Proximal Policy Optimization (PPO) is a powerful reinforcement learning (RL) algorithm. 

### TradingEnv: 
A custom OpenAI Gym environment that simulates a trading environment
### PPOStrategy: 
The main strategy class that utilizes the PPO algorithm for making trading decisions
### Training Scripts: 
Code to train and evaluate the model

### How PPO Works
PPO is a policy gradient reinforcement learning method that's particularly stable for training. 

The agent (PPO model) observes the market state (price data, indicators)
It decides on actions (buy, hold, sell)
It receives rewards based on changes in portfolio value
It learns to optimize its policy to maximize cumulative rewards

## The Trading Environment
The TradingEnv class defines:

### Observation Space: 
Market data features + portfolio status (36 features total)
### Action Space: 
Discrete actions (0=hold, 1=buy, 2=sell)
### Reward Function: 
Based on relative change in net worth from one step to the next

This environment handles the simulation of trades, portfolio value calculation, and providing feedback to the agent.

## Key Components in PPOStrategy

Data Preprocessing: Calculates various technical indicators
Model Training: Creates and trains the PPO model on historical data
Trading Decision Logic: Uses the trained model to determine entries and exits
Position Sizing: Adjusts position size based on model confidence and market conditions
Evaluation: Tests the model on unseen data

### 🧠 PPO Hyperparameters Overview

| **Hyperparameter** | **Current Value** | **Influence on Results** |
|--------------------|------------------|---------------------------|
| `learning_rate`    | 0.0003           | Controls how quickly the model adapts; too high may cause instability, too low may result in slow convergence |
| `n_steps`          | 2048             | Number of steps taken before updating the policy; larger values can improve stability but use more memory |
| `batch_size`       | 64               | Size of mini-batches used during training; affects training stability and speed |
| `n_epochs`         | 10               | Number of training epochs per batch; higher values can extract more information but risk overfitting |
| `gamma`            | 0.99             | Discount factor for future rewards; higher values (closer to 1) prioritize long-term rewards |
| `gae_lambda`       | 0.95             | Controls the trade-off between bias and variance in advantage estimation |
| `clip_range`       | 0.2              | Limits policy update size; prevents too large policy changes |
| `ent_coef`         | 0.01             | Encourages exploration by adding entropy to the policy; higher values promote more exploration |
| `vf_coef`          | 0.5              | Weight of the value function loss in the total loss function |
| `max_grad_norm`    | 0.5              | Clips gradients to prevent exploding gradients |

---

### 🔧 Tuning Recommendations

- **For more aggressive trading:** Increase `ent_coef` to encourage exploration (e.g., `0.02`–`0.05`)
- **For more stable learning:** Decrease `learning_rate` (e.g., `0.0001`) and increase `n_steps`
- **For longer-term strategies:** Increase `gamma` closer to `1.0` (e.g., `0.995`)
- **For more policy stability:** Decrease `clip_range` (e.g., `0.1`)

### 🎯 Fine-Tuning the Reward Function


