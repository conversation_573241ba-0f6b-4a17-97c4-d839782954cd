"""
Walk-Forward Validation for PPO Trading Models

This module implements walk-forward validation to test model robustness
across different time periods and market conditions.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import json

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

from app.training.comprehensive_training_system import (
    ComprehensiveTrainingSystem, TrainingConfig, EvaluationMetrics, ExperimentResult
)
from app.db.db_executor import DatabaseExecutor

logger = logging.getLogger(__name__)


@dataclass
class WalkForwardConfig:
    """Configuration for walk-forward validation"""
    pair: str
    start_date: datetime
    end_date: datetime
    training_months: int = 12  # Months of data for training
    testing_months: int = 3    # Months of data for testing
    step_months: int = 1       # Months to step forward each iteration
    min_training_samples: int = 1000  # Minimum samples required for training
    
    def get_time_windows(self) -> List[Tuple[datetime, datetime, datetime, datetime]]:
        """Generate training and testing time windows"""
        windows = []
        current_start = self.start_date
        
        while True:
            # Calculate training window
            train_start = current_start
            train_end = train_start + timedelta(days=30 * self.training_months)
            
            # Calculate testing window
            test_start = train_end
            test_end = test_start + timedelta(days=30 * self.testing_months)
            
            # Check if we've reached the end date
            if test_end > self.end_date:
                break
            
            windows.append((train_start, train_end, test_start, test_end))
            
            # Step forward
            current_start += timedelta(days=30 * self.step_months)
        
        return windows


@dataclass
class WalkForwardResult:
    """Results from a single walk-forward iteration"""
    iteration: int
    train_start: datetime
    train_end: datetime
    test_start: datetime
    test_end: datetime
    training_samples: int
    testing_samples: int
    metrics: EvaluationMetrics
    model_path: str
    training_time: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'iteration': self.iteration,
            'train_start': self.train_start.isoformat(),
            'train_end': self.train_end.isoformat(),
            'test_start': self.test_start.isoformat(),
            'test_end': self.test_end.isoformat(),
            'training_samples': self.training_samples,
            'testing_samples': self.testing_samples,
            'metrics': self.metrics.to_dict(),
            'model_path': self.model_path,
            'training_time': self.training_time
        }


class WalkForwardValidator:
    """Walk-forward validation system for trading models"""
    
    def __init__(self, db: DatabaseExecutor, output_dir: str = "./walk_forward_results"):
        self.db = db
        self.training_system = ComprehensiveTrainingSystem(db, output_dir)
        self.output_dir = output_dir
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
    
    async def load_data_for_period(self, pair: str, start_date: datetime, 
                                  end_date: datetime) -> pd.DataFrame:
        """Load data for a specific time period"""
        from app.strategy.ppo_strategy import PPOStrategy
        from app.scripts.historical_data_feed import HistoricalDataFeed
        import json
        
        # Create PPO strategy for preprocessing
        ppo_strategy = PPOStrategy(pair=pair)
        
        # Create order book fetcher
        async def ob_fetcher(start_time, end_time):
            query = """
            SELECT pair, snapshot_time, bids, asks, checksum
            FROM order_book_snapshots
            WHERE pair = %s AND snapshot_time BETWEEN %s AND %s
            ORDER BY snapshot_time ASC
            """
            results = self.db.execute_select(query, (pair, start_time, end_time))
            
            if not results:
                return pd.DataFrame()
            
            feature_records = []
            for row in results:
                try:
                    bids = json.loads(row[2]) if isinstance(row[2], str) else row[2]
                    asks = json.loads(row[3]) if isinstance(row[3], str) else row[3]
                    
                    orderbook_data = {
                        'pair': row[0],
                        'snapshot_time': row[1],
                        'bids': bids or [],
                        'asks': asks or [],
                        'checksum': row[4]
                    }
                    
                    ob_features = ppo_strategy.calculate_orderbook_features(orderbook_data)
                    ob_features['timestamp'] = row[1]
                    feature_records.append(ob_features)
                except Exception:
                    continue
            
            return pd.DataFrame(feature_records)
        
        # Create data feed
        data_feed = HistoricalDataFeed(
            db=self.db,
            pair=pair,
            start_date=start_date,
            end_date=end_date,
            preprocess_func=ppo_strategy.preprocess_data,
            order_book_fetcher=ob_fetcher
        )
        
        await data_feed.connect()
        
        if data_feed.candles is None or data_feed.candles.empty:
            return pd.DataFrame()
        
        return data_feed.candles.copy()
    
    async def run_walk_forward_validation(self, wf_config: WalkForwardConfig, 
                                        training_config: TrainingConfig) -> List[WalkForwardResult]:
        """Run complete walk-forward validation"""
        
        logger.info(f"Starting walk-forward validation for {wf_config.pair}")
        logger.info(f"Training window: {wf_config.training_months} months")
        logger.info(f"Testing window: {wf_config.testing_months} months")
        logger.info(f"Step size: {wf_config.step_months} months")
        
        # Get time windows
        windows = wf_config.get_time_windows()
        logger.info(f"Generated {len(windows)} time windows")
        
        results = []
        
        for i, (train_start, train_end, test_start, test_end) in enumerate(windows):
            logger.info(f"\nIteration {i+1}/{len(windows)}")
            logger.info(f"Training: {train_start.date()} to {train_end.date()}")
            logger.info(f"Testing: {test_start.date()} to {test_end.date()}")
            
            try:
                # Load training data
                train_df = await self.load_data_for_period(
                    wf_config.pair, train_start, train_end
                )
                
                if train_df.empty or len(train_df) < wf_config.min_training_samples:
                    logger.warning(f"Insufficient training data: {len(train_df)} samples")
                    continue
                
                # Load testing data
                test_df = await self.load_data_for_period(
                    wf_config.pair, test_start, test_end
                )
                
                if test_df.empty:
                    logger.warning("No testing data available")
                    continue
                
                logger.info(f"Training samples: {len(train_df)}, Testing samples: {len(test_df)}")
                
                # Train model (use only one seed for speed)
                import time
                start_time = time.time()
                
                model, model_path, _ = self.training_system.train_single_model(
                    training_config, train_df, test_df, seed=42
                )
                
                training_time = time.time() - start_time
                
                # Evaluate model
                metrics = self.training_system.evaluate_model(model, test_df, training_config)
                
                # Create result
                result = WalkForwardResult(
                    iteration=i+1,
                    train_start=train_start,
                    train_end=train_end,
                    test_start=test_start,
                    test_end=test_end,
                    training_samples=len(train_df),
                    testing_samples=len(test_df),
                    metrics=metrics,
                    model_path=model_path,
                    training_time=training_time
                )
                
                results.append(result)
                
                logger.info(f"Iteration {i+1} completed:")
                logger.info(f"  Sharpe Ratio: {metrics.sharpe_ratio:.4f}")
                logger.info(f"  Total Return: {metrics.total_return:.2f}%")
                logger.info(f"  Max Drawdown: {metrics.max_drawdown:.2f}%")
                
            except Exception as e:
                logger.error(f"Error in iteration {i+1}: {e}")
                continue
        
        # Save results
        self.save_walk_forward_results(results, wf_config)
        
        return results
    
    def save_walk_forward_results(self, results: List[WalkForwardResult], 
                                 config: WalkForwardConfig):
        """Save walk-forward validation results"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"{self.output_dir}/walk_forward_{config.pair.replace('/', '')}_{timestamp}.json"
        
        # Convert results to serializable format
        serializable_results = {
            'config': asdict(config),
            'results': [result.to_dict() for result in results],
            'summary': self.analyze_walk_forward_results(results)
        }
        
        with open(results_file, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"Walk-forward results saved to {results_file}")
    
    def analyze_walk_forward_results(self, results: List[WalkForwardResult]) -> Dict[str, Any]:
        """Analyze walk-forward validation results"""
        if not results:
            return {"error": "No results to analyze"}
        
        # Extract metrics
        metrics_df = pd.DataFrame([result.metrics.to_dict() for result in results])
        
        # Calculate statistics
        stats = {}
        for metric in ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']:
            if metric in metrics_df.columns:
                stats[metric] = {
                    'mean': float(metrics_df[metric].mean()),
                    'std': float(metrics_df[metric].std()),
                    'min': float(metrics_df[metric].min()),
                    'max': float(metrics_df[metric].max()),
                    'median': float(metrics_df[metric].median())
                }
        
        # Performance consistency
        sharpe_ratios = metrics_df['sharpe_ratio'].values
        positive_periods = (sharpe_ratios > 0).sum()
        consistency_ratio = positive_periods / len(sharpe_ratios)
        
        # Time-based analysis
        time_analysis = {
            'total_periods': len(results),
            'positive_periods': int(positive_periods),
            'consistency_ratio': float(consistency_ratio),
            'avg_training_time': float(np.mean([r.training_time for r in results])),
            'date_range': {
                'start': results[0].train_start.isoformat(),
                'end': results[-1].test_end.isoformat()
            }
        }
        
        # Best and worst periods
        best_idx = metrics_df['sharpe_ratio'].idxmax()
        worst_idx = metrics_df['sharpe_ratio'].idxmin()
        
        performance_extremes = {
            'best_period': {
                'iteration': results[best_idx].iteration,
                'test_period': f"{results[best_idx].test_start.date()} to {results[best_idx].test_end.date()}",
                'sharpe_ratio': float(results[best_idx].metrics.sharpe_ratio),
                'total_return': float(results[best_idx].metrics.total_return)
            },
            'worst_period': {
                'iteration': results[worst_idx].iteration,
                'test_period': f"{results[worst_idx].test_start.date()} to {results[worst_idx].test_end.date()}",
                'sharpe_ratio': float(results[worst_idx].metrics.sharpe_ratio),
                'total_return': float(results[worst_idx].metrics.total_return)
            }
        }
        
        return {
            'statistics': stats,
            'time_analysis': time_analysis,
            'performance_extremes': performance_extremes
        }
    
    def print_walk_forward_summary(self, results: List[WalkForwardResult]):
        """Print a summary of walk-forward validation results"""
        if not results:
            logger.info("No results to summarize")
            return
        
        analysis = self.analyze_walk_forward_results(results)
        
        logger.info("\n" + "="*60)
        logger.info("WALK-FORWARD VALIDATION SUMMARY")
        logger.info("="*60)
        
        logger.info(f"Total periods tested: {analysis['time_analysis']['total_periods']}")
        logger.info(f"Positive periods: {analysis['time_analysis']['positive_periods']}")
        logger.info(f"Consistency ratio: {analysis['time_analysis']['consistency_ratio']:.2%}")
        
        logger.info("\nPerformance Statistics:")
        for metric, stats in analysis['statistics'].items():
            logger.info(f"  {metric}:")
            logger.info(f"    Mean: {stats['mean']:.4f}")
            logger.info(f"    Std:  {stats['std']:.4f}")
            logger.info(f"    Range: [{stats['min']:.4f}, {stats['max']:.4f}]")
        
        logger.info("\nBest Period:")
        best = analysis['performance_extremes']['best_period']
        logger.info(f"  Period: {best['test_period']}")
        logger.info(f"  Sharpe: {best['sharpe_ratio']:.4f}")
        logger.info(f"  Return: {best['total_return']:.2f}%")
        
        logger.info("\nWorst Period:")
        worst = analysis['performance_extremes']['worst_period']
        logger.info(f"  Period: {worst['test_period']}")
        logger.info(f"  Sharpe: {worst['sharpe_ratio']:.4f}")
        logger.info(f"  Return: {worst['total_return']:.2f}%")
        
        logger.info("="*60)


async def main():
    """Example usage of walk-forward validation"""
    
    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME'),
        db_user=os.getenv('DB_USER'),
        db_password=os.getenv('DB_PASSWORD'),
        db_host=os.getenv('DB_HOST'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    # Create walk-forward configuration
    wf_config = WalkForwardConfig(
        pair="SOL/USD",
        start_date=datetime(2022, 1, 1, tzinfo=timezone.utc),
        end_date=datetime(2025, 6, 1, tzinfo=timezone.utc),
        training_months=6,
        testing_months=2,
        step_months=1
    )
    
    # Create training configuration
    training_config = TrainingConfig(
        pair="SOL/USD",
        total_timesteps=50000,  # Reduced for faster validation
        random_seeds=[42]  # Single seed for speed
    )
    
    # Run walk-forward validation
    validator = WalkForwardValidator(db)
    results = await validator.run_walk_forward_validation(wf_config, training_config)
    
    # Print summary
    validator.print_walk_forward_summary(results)
    
    db.close()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
