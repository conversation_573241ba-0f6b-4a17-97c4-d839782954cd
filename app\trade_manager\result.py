from dataclasses import dataclass
from datetime import datetime


@dataclass
class Result:
    strategy_name: str
    entry_trade_id: str
    exit_trade_id: str
    entry_price: float
    exit_price: float
    entry_fee: float
    exit_fee: float
    entry_timestamp: datetime
    exit_timestamp: datetime
    volume: float
    profit_loss: float
    duration: float
    success: bool
    exit_reason: str

    def to_dict(self):
        return {
            'strategy_name': self.strategy_name,
            'entry_trade_id': self.entry_trade_id,
            'exit_trade_id': self.exit_trade_id,
            'entry_price': self.entry_price,
            'exit_price': self.exit_price,
            'entry_fee': self.entry_fee,
            'exit_fee': self.exit_fee,
            'entry_timestamp': self.entry_timestamp,
            'exit_timestamp': self.exit_timestamp,
            'volume': self.volume,
            'profit_loss': self.profit_loss,
            'duration': self.duration,
            'success': self.success,
            'exit_reason': self.exit_reason
        }
