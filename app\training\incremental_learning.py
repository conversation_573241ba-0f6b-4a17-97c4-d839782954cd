"""
Incremental Learning System for Live Trading

This module implements incremental learning for PPO models in live trading,
allowing continuous model updates with new market data while preserving
learned knowledge.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import torch
from datetime import datetime, timezone, timedelta
from typing import Optional, Any
from dataclasses import dataclass
import json
from pathlib import Path

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

from stable_baselines3 import PPO
from app.training.comprehensive_training_system import (
    ComprehensiveTrainingSystem, TrainingConfig, EvaluationMetrics
)
from app.db.db_executor import DatabaseExecutor

logger = logging.getLogger(__name__)


@dataclass
class IncrementalConfig:
    """Configuration for incremental learning"""
    pair: str
    model_path: str
    update_frequency_days: int = 7  # How often to update the model
    training_window_months: int = 18  # Months of recent data to use
    validation_window_months: int = 3  # Months for validation
    min_new_samples: int = 100  # Minimum new samples to trigger update
    learning_rate_decay: float = 0.8  # Decay factor for learning rate
    ensemble_weight: float = 0.8  # Weight for existing model in ensemble
    max_updates_per_session: int = 5  # Maximum updates in one session
    performance_threshold: float = 0.1  # Minimum Sharpe improvement to keep update

    def __post_init__(self):
        self.model_path = Path(self.model_path)
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")


@dataclass
class UpdateResult:
    """Results from a model update"""
    update_id: str
    timestamp: datetime
    new_samples: int
    training_samples: int
    validation_samples: int
    old_metrics: EvaluationMetrics
    new_metrics: EvaluationMetrics
    improvement: float
    model_updated: bool
    new_model_path: Optional[str]
    training_time: float

    def to_dict(self) -> dict[str, Any]:
        return {
            'update_id': self.update_id,
            'timestamp': self.timestamp.isoformat(),
            'new_samples': self.new_samples,
            'training_samples': self.training_samples,
            'validation_samples': self.validation_samples,
            'old_metrics': self.old_metrics.to_dict(),
            'new_metrics': self.new_metrics.to_dict(),
            'improvement': self.improvement,
            'model_updated': self.model_updated,
            'new_model_path': self.new_model_path,
            'training_time': self.training_time
        }


class IncrementalLearningSystem:
    """System for incremental learning in live trading"""

    def __init__(self, db: DatabaseExecutor, output_dir: str = "./incremental_models"):
        self.db = db
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories
        (self.output_dir / "models").mkdir(exist_ok=True)
        (self.output_dir / "logs").mkdir(exist_ok=True)
        (self.output_dir / "backups").mkdir(exist_ok=True)

        self.training_system = ComprehensiveTrainingSystem(db, str(self.output_dir))
        self.update_history: list[UpdateResult] = []

    async def get_latest_data(self, pair: str, start_date: datetime,
                            end_date: datetime) -> pd.DataFrame:
        """Get the latest market data for training"""
        from app.strategy.ppo_strategy import PPOStrategy
        from app.scripts.historical_data_feed import HistoricalDataFeed

        # Create PPO strategy for preprocessing
        ppo_strategy = PPOStrategy(pair=pair)

        # Create order book fetcher
        async def ob_fetcher(start_time, end_time):
            query = """
            SELECT pair, snapshot_time, bids, asks, checksum
            FROM order_book_snapshots
            WHERE pair = %s AND snapshot_time BETWEEN %s AND %s
            ORDER BY snapshot_time ASC
            """
            results = self.db.execute_select(query, (pair, start_time, end_time))

            if not results:
                return pd.DataFrame()

            feature_records = []
            for row in results:
                try:
                    bids = json.loads(row[2]) if isinstance(row[2], str) else row[2]
                    asks = json.loads(row[3]) if isinstance(row[3], str) else row[3]

                    orderbook_data = {
                        'pair': row[0],
                        'snapshot_time': row[1],
                        'bids': bids or [],
                        'asks': asks or [],
                        'checksum': row[4]
                    }

                    ob_features = ppo_strategy.calculate_orderbook_features(orderbook_data)
                    ob_features['timestamp'] = row[1]
                    feature_records.append(ob_features)
                except Exception:
                    continue

            return pd.DataFrame(feature_records)

        # Create data feed
        data_feed = HistoricalDataFeed(
            db=self.db,
            pair=pair,
            start_date=start_date,
            end_date=end_date,
            preprocess_func=ppo_strategy.preprocess_data,
            order_book_fetcher=ob_fetcher
        )

        await data_feed.connect()

        if data_feed.candles is None or data_feed.candles.empty:
            return pd.DataFrame()

        return data_feed.candles.copy()

    def get_last_training_date(self, model_path: str) -> Optional[datetime]:
        """Get the last training date from model metadata"""
        metadata_file = Path(model_path).with_suffix('.json')

        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                return datetime.fromisoformat(metadata.get('last_training_date'))
            except Exception as e:
                logger.warning(f"Could not read metadata: {e}")

        return None

    def save_model_metadata(self, model_path: str, metadata: dict[str, Any]):
        """Save model metadata"""
        metadata_file = Path(model_path).with_suffix('.json')

        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)

    def backup_model(self, model_path: str) -> str:
        """Create a backup of the current model"""
        import shutil

        model_path = Path(model_path)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = self.output_dir / "backups" / f"{model_path.stem}_backup_{timestamp}.zip"

        shutil.copy2(model_path, backup_path)

        # Also backup metadata if it exists
        metadata_file = model_path.with_suffix('.json')
        if metadata_file.exists():
            backup_metadata = backup_path.with_suffix('.json')
            shutil.copy2(metadata_file, backup_metadata)

        logger.info(f"Model backed up to {backup_path}")
        return str(backup_path)

    async def check_for_updates(self, config: IncrementalConfig) -> bool:
        """Check if model should be updated based on new data availability"""

        # Get last training date
        last_training_date = self.get_last_training_date(str(config.model_path))

        if last_training_date is None:
            logger.info("No last training date found, assuming update needed")
            return True

        # Check if enough time has passed
        days_since_update = (datetime.now(timezone.utc) - last_training_date).days

        if days_since_update < config.update_frequency_days:
            logger.info(f"Only {days_since_update} days since last update, skipping")
            return False

        # Check if enough new data is available
        end_date = datetime.now(timezone.utc)
        start_date = last_training_date

        new_data = await self.get_latest_data(config.pair, start_date, end_date)

        if len(new_data) < config.min_new_samples:
            logger.info(f"Only {len(new_data)} new samples, minimum {config.min_new_samples} required")
            return False

        logger.info(f"Update needed: {len(new_data)} new samples available")
        return True

    async def perform_incremental_update(self, config: IncrementalConfig) -> UpdateResult:
        """Perform incremental model update"""
        import time
        start_time = time.time()

        update_id = f"update_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"Starting incremental update: {update_id}")

        # Load existing model
        logger.info(f"Loading existing model from {config.model_path}")
        old_model = PPO.load(str(config.model_path))

        # Get training data window
        end_date = datetime.now(timezone.utc)
        train_start = end_date - timedelta(days=30 * config.training_window_months)
        val_start = end_date - timedelta(days=30 * config.validation_window_months)

        # Load data
        logger.info(f"Loading training data from {train_start.date()} to {val_start.date()}")
        train_data = await self.get_latest_data(config.pair, train_start, val_start)

        logger.info(f"Loading validation data from {val_start.date()} to {end_date.date()}")
        val_data = await self.get_latest_data(config.pair, val_start, end_date)

        if train_data.empty or val_data.empty:
            raise ValueError("Insufficient data for incremental update")

        # Calculate new samples (data since last update)
        last_training_date = self.get_last_training_date(str(config.model_path))
        if last_training_date:
            new_data = train_data[train_data['timestamp'] > last_training_date]
            new_samples = len(new_data)
        else:
            new_samples = len(train_data)

        logger.info(f"Training samples: {len(train_data)}, Validation samples: {len(val_data)}")
        logger.info(f"New samples since last update: {new_samples}")

        # Evaluate old model
        old_metrics = self.training_system.evaluate_model(
            old_model, val_data,
            TrainingConfig(pair=config.pair)
        )

        logger.info(f"Old model performance - Sharpe: {old_metrics.sharpe_ratio:.4f}")

        # Create new model with reduced learning rate
        from app.strategy.ppo_strategy import PPOStrategy
        from app.strategy.trading_env import TradingEnv

        strategy = PPOStrategy(pair=config.pair)
        train_env = TradingEnv(train_data, strategy)

        # Get current learning rate and reduce it
        current_lr = old_model.learning_rate
        if callable(current_lr):
            # If it's a schedule, use a fixed reduced rate
            new_lr = 0.0001 * config.learning_rate_decay
        else:
            new_lr = current_lr * config.learning_rate_decay

        logger.info(f"Using reduced learning rate: {new_lr}")

        # Create new model with same architecture but reduced learning rate
        new_model = PPO(
            "MlpPolicy",
            train_env,
            learning_rate=new_lr,
            n_steps=old_model.n_steps,
            batch_size=old_model.batch_size,
            n_epochs=old_model.n_epochs,
            gamma=old_model.gamma,
            gae_lambda=old_model.gae_lambda,
            clip_range=old_model.clip_range,
            ent_coef=old_model.ent_coef,
            vf_coef=old_model.vf_coef,
            max_grad_norm=old_model.max_grad_norm,
            verbose=1
        )

        # Copy weights from old model
        new_model.policy.load_state_dict(old_model.policy.state_dict())

        # Continue training with new data
        logger.info("Continuing training with new data...")
        new_model.learn(
            total_timesteps=min(50000, len(train_data) * 10),  # Adaptive timesteps
            reset_num_timesteps=False,
            progress_bar=True
        )

        # Evaluate new model
        new_metrics = self.training_system.evaluate_model(
            new_model, val_data,
            TrainingConfig(pair=config.pair)
        )

        logger.info(f"New model performance - Sharpe: {new_metrics.sharpe_ratio:.4f}")

        # Calculate improvement
        improvement = new_metrics.sharpe_ratio - old_metrics.sharpe_ratio
        logger.info(f"Performance improvement: {improvement:.4f}")

        # Decide whether to keep the update
        model_updated = improvement >= config.performance_threshold
        new_model_path = None

        if model_updated:
            # Backup old model
            backup_path = self.backup_model(str(config.model_path))

            # Save new model
            new_model.save(str(config.model_path))
            new_model_path = str(config.model_path)

            # Update metadata
            metadata = {
                'last_training_date': end_date.isoformat(),
                'update_id': update_id,
                'improvement': improvement,
                'training_samples': len(train_data),
                'validation_samples': len(val_data),
                'backup_path': backup_path
            }
            self.save_model_metadata(str(config.model_path), metadata)

            logger.info(f"Model updated successfully. Backup saved to {backup_path}")
        else:
            logger.info(f"Model not updated - improvement {improvement:.4f} below threshold {config.performance_threshold}")

        training_time = time.time() - start_time

        # Create result
        result = UpdateResult(
            update_id=update_id,
            timestamp=datetime.now(timezone.utc),
            new_samples=new_samples,
            training_samples=len(train_data),
            validation_samples=len(val_data),
            old_metrics=old_metrics,
            new_metrics=new_metrics,
            improvement=improvement,
            model_updated=model_updated,
            new_model_path=new_model_path,
            training_time=training_time
        )

        self.update_history.append(result)
        self.save_update_history()

        return result

    def save_update_history(self):
        """Save update history to file"""
        history_file = self.output_dir / "logs" / "update_history.json"

        history_data = [result.to_dict() for result in self.update_history]

        with open(history_file, 'w') as f:
            json.dump(history_data, f, indent=2, default=str)

    async def run_incremental_learning_cycle(self, config: IncrementalConfig) -> Optional[UpdateResult]:
        """Run a complete incremental learning cycle"""

        logger.info(f"Starting incremental learning cycle for {config.pair}")

        try:
            # Check if update is needed
            if not await self.check_for_updates(config):
                logger.info("No update needed at this time")
                return None

            # Perform update
            result = await self.perform_incremental_update(config)

            logger.info(f"Incremental learning cycle completed")
            logger.info(f"Model updated: {result.model_updated}")
            logger.info(f"Performance improvement: {result.improvement:.4f}")

            return result

        except Exception as e:
            logger.error(f"Error in incremental learning cycle: {e}")
            raise


async def main():
    """Example usage of incremental learning system"""

    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME'),
        db_user=os.getenv('DB_USER'),
        db_password=os.getenv('DB_PASSWORD'),
        db_host=os.getenv('DB_HOST'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )

    # Create incremental learning configuration
    config = IncrementalConfig(
        pair="SOL/USD",
        model_path="./models/best_ppo_model.zip",  # Path to existing model
        update_frequency_days=7,
        training_window_months=12,
        validation_window_months=2,
        min_new_samples=500,
        performance_threshold=0.05
    )

    # Run incremental learning
    system = IncrementalLearningSystem(db)
    result = await system.run_incremental_learning_cycle(config)

    if result:
        print(f"Update completed: {result.model_updated}")
        print(f"Performance improvement: {result.improvement:.4f}")
    else:
        print("No update performed")

    db.close()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
