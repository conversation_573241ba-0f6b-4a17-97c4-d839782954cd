# No datetime imports needed

class PortfolioManager:
    def __init__(self, initial_balance: float, api, asset: str, base_currency: str, max_daily_values: int = 120):
        """Initialize the PortfolioManager for live trading.

        Args:
            initial_balance: Initial balance in base currency (e.g., USD).
            exchange_api: Object providing exchange API methods (e.g., get_account_info, place_order).
            asset: Traded asset symbol (e.g., 'SOL').
            base_currency: Base currency symbol (e.g., 'USD').
            max_daily_values: Maximum number of daily values to store (default: 365).
        """
        self.initial_balance = initial_balance
        self.api = api
        self.asset = asset  # e.g., 'SOL'
        self.base_currency = base_currency  # e.g., 'USD'

        # Balances
        self.current_balance = initial_balance  # Base currency balance
        self.asset_balance = 0.0  # Asset balance (e.g., SOL held)
        self.reserved_funds = 0.0  # Base currency reserved for buy orders
        self.reserved_assets = 0.0  # Assets reserved for sell orders

        # Maximum number of daily values to store
        self.max_daily_values = max_daily_values
        self.daily_values = []  # Historical portfolio values
        # List of open positions (optional for advanced tracking)
        self.open_positions = []

    async def sync_with_exchange(self, active_trades):
        """Synchronize portfolio state with the exchange."""
        balance = self.api.get_account_balance()
        self.current_balance = float(
            balance['result'].get('Z' + self.base_currency, 0))
        # self.asset_balance =  float(balance['result'].get(self.asset, 0))  #.get('X'+self.asset, 0))
        # positions = self.api.get_closed_orders()
        # self.open_positions = positions['result'] if positions.get('result') else {}
        # for pos_tx in self.open_positions:
        for pos_tx in active_trades:
            # Assuming pos_tx is a dictionary with keys 'vol' and 'cost'
            pos = active_trades[pos_tx]
            self.reserved_assets += float(pos.remaining_volume)
            self.reserved_funds = float(
                pos.remaining_volume) * float(pos.entry_price)

    def order_filled(self, order_type: str, price: float, volume: float, fee: float):
        """Update portfolio when an order is filled.

        Args:
            order_type: 'buy' or 'sell'.
            price: Execution price in base currency per unit of asset.
            volume: Executed volume of asset.
            fee: Fee in base currency.
        """
        if order_type == 'buy':
            self.asset_balance += volume
            self.reserved_funds -= price * volume  # Release reserved funds
            self.current_balance -= fee  # Deduct fee
        elif order_type == 'sell':
            proceeds = price * volume
            self.current_balance += proceeds - fee  # Add proceeds minus fee
            self.reserved_assets -= volume  # Release reserved assets

    def release_funds(self, amount: float, is_asset: bool = False):
        """Release reserved funds or assets (e.g., if an order is canceled).

        Args:
            amount: Amount to release.
            is_asset: True if releasing asset, False if releasing base currency.
        """
        if is_asset:
            self.reserved_assets -= amount
            self.asset_balance += amount
        else:
            self.reserved_funds -= amount
            self.current_balance += amount

    def record_daily_value(self, timestamp, current_price: float):
        """Record the portfolio's total value at a given timestamp.

        Maintains a limited history based on max_daily_values to prevent memory leaks.
        """
        total_value = self.get_current_total_value(current_price)
        self.daily_values.append((timestamp, total_value))

        # Prevent memory leak by limiting the size of daily_values
        if len(self.daily_values) > self.max_daily_values:
            # Remove oldest values to maintain the size limit
            self.daily_values = self.daily_values[-self.max_daily_values:]

    def add_realized_pnl(self, realized_pnl: float):
        """Add realized profit/loss from a closed trade."""
        self.current_balance += realized_pnl

    def get_current_total_value(self, current_price: float) -> float:
        """Calculate the total portfolio value including unrealized PnL.

        Args:
            current_price: Current market price of the asset in base currency.

        Returns:
            float: Total portfolio value.
        """
        # Base currency + value of asset holdings (including reserved assets)
        total_value = self.current_balance + \
            (self.asset_balance + self.reserved_assets) * \
            float(current_price) + self.reserved_funds
        return total_value

    def reserve_funds(self, amount: float, is_asset: bool = False) -> bool:
        """Reserve funds or assets for an order.

        Args:
            amount: Amount to reserve.
            is_asset: True if reserving asset, False if reserving base currency.

        Returns:
            bool: True if reservation was successful, False otherwise.
        """
        if is_asset:
            if self.asset_balance >= amount:
                self.asset_balance -= amount
                self.reserved_assets += amount
                return True
        else:
            if self.current_balance >= amount:
                self.current_balance -= amount
                self.reserved_funds += amount
                return True
        return False
